-- =============================================
-- إصلاح أعمدة جدول الصيدليات في قاعدة البيانات UnifiedPharmacy
-- Fix Missing Pharmacy Table Columns in UnifiedPharmacy Database
-- =============================================

USE UnifiedPharmacy;
GO

-- التحقق من وجود الجدول أولاً
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    -- إنشاء الجدول إذا لم يكن موجوداً
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyName NVARCHAR(100) NOT NULL,
        pharmacyNameAr NVARCHAR(100) NOT NULL,
        ownerName NVARCHAR(100) NOT NULL,
        ownerNameAr NVARCHAR(100) NOT NULL,
        licenseNumber NVARCHAR(50) UNIQUE NOT NULL,
        taxNumber NVARCHAR(50),
        email NVARCHAR(100) UNIQUE NOT NULL,
        phone NVARCHAR(20) NOT NULL,
        mobile NVARCHAR(20),
        address NVARCHAR(200) NOT NULL,
        addressAr NVARCHAR(200) NOT NULL,
        city NVARCHAR(50) NOT NULL,
        cityAr NVARCHAR(50) NOT NULL,
        region NVARCHAR(50),
        regionAr NVARCHAR(50),
        postalCode NVARCHAR(10),
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        status NVARCHAR(20) NOT NULL DEFAULT 'Pending',
        isActive BIT NOT NULL DEFAULT 1,
        registrationDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        approvalDate DATETIME2,
        approvedBy INT,
        lastActivityDate DATETIME2,
        subscriptionStatus NVARCHAR(20) NOT NULL DEFAULT 'Trial',
        subscriptionStartDate DATETIME2 DEFAULT GETDATE(),
        subscriptionEndDate DATETIME2,
        notes NVARCHAR(300)
    );
    PRINT 'تم إنشاء جدول pharmacies في قاعدة البيانات UnifiedPharmacy';
END
ELSE
BEGIN
    PRINT 'جدول pharmacies موجود مسبقاً في قاعدة البيانات UnifiedPharmacy';
END

-- التحقق من وجود الأعمدة المفقودة وإضافتها
PRINT 'بدء إصلاح أعمدة جدول الصيدليات...';

-- إضافة عمود subscriptionStatus إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionStatus')
BEGIN
    ALTER TABLE pharmacies ADD subscriptionStatus NVARCHAR(20) NOT NULL DEFAULT 'Trial';
    PRINT 'تم إضافة عمود subscriptionStatus';
END

-- إضافة عمود subscriptionStartDate إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionStartDate')
BEGIN
    ALTER TABLE pharmacies ADD subscriptionStartDate DATETIME2 DEFAULT GETDATE();
    PRINT 'تم إضافة عمود subscriptionStartDate';
END

-- إضافة عمود subscriptionEndDate إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionEndDate')
BEGIN
    ALTER TABLE pharmacies ADD subscriptionEndDate DATETIME2;
    PRINT 'تم إضافة عمود subscriptionEndDate';
END

-- إضافة عمود approvedBy إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'approvedBy')
BEGIN
    ALTER TABLE pharmacies ADD approvedBy INT;
    PRINT 'تم إضافة عمود approvedBy';
END

-- إضافة عمود approvalDate إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'approvalDate')
BEGIN
    ALTER TABLE pharmacies ADD approvalDate DATETIME2;
    PRINT 'تم إضافة عمود approvalDate';
END

-- إضافة عمود lastActivityDate إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'lastActivityDate')
BEGIN
    ALTER TABLE pharmacies ADD lastActivityDate DATETIME2;
    PRINT 'تم إضافة عمود lastActivityDate';
END

-- إضافة عمود notes إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'notes')
BEGIN
    ALTER TABLE pharmacies ADD notes NVARCHAR(300);
    PRINT 'تم إضافة عمود notes';
END

-- إضافة عمود isActive إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'isActive')
BEGIN
    ALTER TABLE pharmacies ADD isActive BIT NOT NULL DEFAULT 1;
    PRINT 'تم إضافة عمود isActive';
END

-- إضافة عمود registrationDate إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'registrationDate')
BEGIN
    ALTER TABLE pharmacies ADD registrationDate DATETIME2 NOT NULL DEFAULT GETDATE();
    PRINT 'تم إضافة عمود registrationDate';
END

-- إضافة عمود taxNumber إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'taxNumber')
BEGIN
    ALTER TABLE pharmacies ADD taxNumber NVARCHAR(50);
    PRINT 'تم إضافة عمود taxNumber';
END

-- إضافة عمود mobile إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'mobile')
BEGIN
    ALTER TABLE pharmacies ADD mobile NVARCHAR(20);
    PRINT 'تم إضافة عمود mobile';
END

-- إضافة عمود region إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'region')
BEGIN
    ALTER TABLE pharmacies ADD region NVARCHAR(50);
    PRINT 'تم إضافة عمود region';
END

-- إضافة عمود regionAr إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'regionAr')
BEGIN
    ALTER TABLE pharmacies ADD regionAr NVARCHAR(50);
    PRINT 'تم إضافة عمود regionAr';
END

-- إضافة عمود postalCode إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'postalCode')
BEGIN
    ALTER TABLE pharmacies ADD postalCode NVARCHAR(10);
    PRINT 'تم إضافة عمود postalCode';
END

-- تحديث البيانات الموجودة
UPDATE pharmacies 
SET subscriptionStartDate = ISNULL(subscriptionStartDate, GETDATE()),
    subscriptionEndDate = ISNULL(subscriptionEndDate, DATEADD(MONTH, 1, GETDATE())),
    lastActivityDate = ISNULL(lastActivityDate, GETDATE()),
    registrationDate = ISNULL(registrationDate, GETDATE()),
    isActive = ISNULL(isActive, 1),
    subscriptionStatus = ISNULL(subscriptionStatus, 'Trial')
WHERE id IS NOT NULL;

PRINT 'تم إصلاح جدول الصيدليات في قاعدة البيانات UnifiedPharmacy بنجاح!';

-- عرض بنية الجدول المحدثة
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'pharmacies'
ORDER BY ORDINAL_POSITION;

-- عرض عدد الصيدليات
SELECT COUNT(*) as TotalPharmacies FROM pharmacies;

PRINT 'تم عرض بنية الجدول المحدثة وعدد الصيدليات';
