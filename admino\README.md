# نظام إدارة الصيدليات المركزي

## نظرة عامة
هذا نظام إدارة مركزي للصيدليات يتيح للمديرين إدارة شبكة من الصيدليات من خلال واجهة موحدة.

## المتطلبات
- .NET Framework 4.8
- SQL Server (LocalDB أو SQL Server Express)
- Guna.UI2.WinForms 2.0.4.7

## قواعد البيانات
النظام يستخدم قاعدتي بيانات:

### 1. PharmacyAdminSystem (قاعدة البيانات المركزية)
تحتوي على:
- بيانات المديرين والصلاحيات
- سجلات النشاطات والمراقبة
- إعدادات النظام
- النسخ الاحتياطية

### 2. pharmacy (قاعدة بيانات الصيدلية)
تحتوي على:
- بيانات الصيدليات المسجلة
- شبكة الصيدليات
- طلبات التبادل بين الصيدليات

## إعداد النظام

### 1. إنشاء قواعد البيانات
قم بتشغيل الملفات التالية في SQL Server:
```sql
-- إنشاء قاعدة البيانات المركزية
-- نفذ محتويات الجزء الأول والثاني من PharmacyAdminSystem

-- إنشاء قاعدة بيانات الصيدلية
-- نفذ محتويات قاعدة بيانات pharmacy
```

### 2. تحديث سلاسل الاتصال
في ملف `DatabaseConnection.cs`، تأكد من صحة سلاسل الاتصال:
```csharp
private static readonly string AdminConnectionString = 
    "Data Source=.;Initial Catalog=PharmacyAdminSystem;Integrated Security=True;...";

private static readonly string PharmacyConnectionString = 
    "Data Source=.;Initial Catalog=pharmacy;Integrated Security=True;...";
```

## بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** Admin@123

## الميزات الرئيسية

### 1. إدارة الصيدليات
- إضافة صيدليات جديدة
- تعديل بيانات الصيدليات
- عرض قائمة الصيدليات
- البحث في الصيدليات
- تفعيل/إلغاء تفعيل الصيدليات

### 2. نظام الصلاحيات
- 4 أدوار رئيسية: SuperAdmin, Admin, Moderator, Viewer
- 23 صلاحية مختلفة
- تحكم دقيق في الوصول

### 3. المراقبة والسجلات
- تسجيل جميع النشاطات
- مراقبة تسجيل الدخول
- إحصائيات الاستخدام
- سجلات الأخطاء

### 4. النسخ الاحتياطية
- نسخ احتياطية مجدولة
- إدارة النسخ الاحتياطية
- استعادة البيانات

## استخدام النظام

### تسجيل الدخول
1. شغل التطبيق
2. أدخل اسم المستخدم وكلمة المرور
3. اضغط "تسجيل الدخول"

### إضافة صيدلية جديدة
1. اضغط على "إضافة صيدلية"
2. املأ البيانات المطلوبة:
   - رمز الصيدلية (يمكن توليده تلقائياً)
   - اسم الصيدلية
   - اسم المالك
   - رقم الترخيص
   - العنوان والمدينة
   - بيانات الاتصال
   - نوع الاشتراك
3. اضغط "حفظ الصيدلية"

### عرض قائمة الصيدليات
1. اضغط على "قائمة الصيدليات"
2. يمكنك:
   - البحث في الصيدليات
   - تعديل بيانات صيدلية (اضغط "تعديل")
   - عرض تفاصيل صيدلية (اضغط مرتين على الصف)
   - تحديث القائمة

## الملفات الرئيسية

### كلاسات الاتصال والإدارة
- `DatabaseConnection.cs` - إدارة الاتصال بقواعد البيانات
- `AdminUserManager.cs` - إدارة المستخدمين وتسجيل الدخول
- `PharmacyManager.cs` - إدارة الصيدليات

### النماذج والواجهات
- `LoginForm.cs` - نموذج تسجيل الدخول
- `TestForm.cs` - النموذج الرئيسي للاختبار
- `add_pharmacy.cs` - صفحة إضافة/تعديل الصيدليات
- `uc_pharmacylist.cs` - صفحة عرض قائمة الصيدليات

## الأمان
- تشفير كلمات المرور
- تسجيل جميع النشاطات
- قفل الحساب بعد محاولات فاشلة
- انتهاء صلاحية الجلسات

## التطوير المستقبلي
- إضافة المزيد من التقارير
- تطوير نظام الإشعارات
- تحسين واجهة المستخدم
- إضافة ميزات الشبكة بين الصيدليات

## الدعم الفني
للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---
**تم تطوير النظام باستخدام C# WinForms و Guna.UI2**
