-- =============================================
-- إعداد قواعد البيانات لنظام إدارة الصيدليات
-- Database Setup for Pharmacy Management System
-- =============================================

-- التحقق من وجود قاعدة البيانات المركزية وإنشاؤها
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'PharmacyAdminSystem')
BEGIN
    CREATE DATABASE PharmacyAdminSystem COLLATE Arabic_CI_AS;
    PRINT 'تم إنشاء قاعدة البيانات PharmacyAdminSystem';
END
ELSE
BEGIN
    PRINT 'قاعدة البيانات PharmacyAdminSystem موجودة مسبقاً';
END
GO

-- التحقق من وجود قاعدة بيانات الصيدلية وإنشاؤها
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'pharmacy')
BEGIN
    CREATE DATABASE pharmacy COLLATE Arabic_CI_AS;
    PRINT 'تم إنشاء قاعدة البيانات pharmacy';
END
ELSE
BEGIN
    PRINT 'قاعدة البيانات pharmacy موجودة مسبقاً';
END
GO

-- =============================================
-- إعداد قاعدة البيانات المركزية
-- =============================================
USE PharmacyAdminSystem;
GO

-- التحقق من وجود الجداول وإنشاؤها إذا لم تكن موجودة

-- جدول المديرين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='admin_users' AND xtype='U')
BEGIN
    CREATE TABLE admin_users (
        id INT IDENTITY(1,1) PRIMARY KEY,
        username NVARCHAR(50) UNIQUE NOT NULL,
        password NVARCHAR(255) NOT NULL,
        fullName NVARCHAR(100) NOT NULL,
        fullNameAr NVARCHAR(100) NOT NULL,
        email NVARCHAR(100) UNIQUE NOT NULL,
        phone NVARCHAR(20),
        role NVARCHAR(20) NOT NULL DEFAULT 'Admin',
        isActive BIT NOT NULL DEFAULT 1,
        lastLogin DATETIME2,
        loginAttempts INT NOT NULL DEFAULT 0,
        lockedUntil DATETIME2,
        createdDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        createdBy INT,
        modifiedDate DATETIME2,
        modifiedBy INT,
        profileImage NVARCHAR(255),
        notes NVARCHAR(500),
        sessionToken NVARCHAR(255),
        sessionExpiry DATETIME2
    );
    PRINT 'تم إنشاء جدول admin_users';
END

-- جدول الصلاحيات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='admin_permissions' AND xtype='U')
BEGIN
    CREATE TABLE admin_permissions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        permissionName NVARCHAR(50) UNIQUE NOT NULL,
        permissionNameAr NVARCHAR(50) NOT NULL,
        description NVARCHAR(200),
        descriptionAr NVARCHAR(200),
        category NVARCHAR(30) NOT NULL,
        isActive BIT NOT NULL DEFAULT 1,
        createdDate DATETIME2 NOT NULL DEFAULT GETDATE()
    );
    PRINT 'تم إنشاء جدول admin_permissions';
END

-- جدول ربط المديرين بالصلاحيات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='admin_user_permissions' AND xtype='U')
BEGIN
    CREATE TABLE admin_user_permissions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        adminId INT NOT NULL,
        permissionId INT NOT NULL,
        grantedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        grantedBy INT NOT NULL,
        isActive BIT NOT NULL DEFAULT 1,
        expiryDate DATETIME2,
        FOREIGN KEY (adminId) REFERENCES admin_users(id),
        FOREIGN KEY (permissionId) REFERENCES admin_permissions(id),
        FOREIGN KEY (grantedBy) REFERENCES admin_users(id),
        UNIQUE(adminId, permissionId)
    );
    PRINT 'تم إنشاء جدول admin_user_permissions';
END

-- جدول سجل النشاطات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='admin_activity_log' AND xtype='U')
BEGIN
    CREATE TABLE admin_activity_log (
        id INT IDENTITY(1,1) PRIMARY KEY,
        adminId INT NOT NULL,
        action NVARCHAR(50) NOT NULL,
        actionAr NVARCHAR(50) NOT NULL,
        targetType NVARCHAR(30) NOT NULL,
        targetId INT,
        targetName NVARCHAR(100),
        details NVARCHAR(500),
        detailsAr NVARCHAR(500),
        timestamp DATETIME2 NOT NULL DEFAULT GETDATE(),
        ipAddress NVARCHAR(45),
        userAgent NVARCHAR(200),
        sessionId NVARCHAR(50),
        severity NVARCHAR(20) DEFAULT 'Info',
        FOREIGN KEY (adminId) REFERENCES admin_users(id)
    );
    PRINT 'تم إنشاء جدول admin_activity_log';
END

-- جدول سجل تسجيل الدخول
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='admin_login_log' AND xtype='U')
BEGIN
    CREATE TABLE admin_login_log (
        id INT IDENTITY(1,1) PRIMARY KEY,
        adminId INT,
        username NVARCHAR(50) NOT NULL,
        loginTime DATETIME2 NOT NULL DEFAULT GETDATE(),
        logoutTime DATETIME2,
        ipAddress NVARCHAR(45),
        userAgent NVARCHAR(200),
        status NVARCHAR(20) NOT NULL,
        failureReason NVARCHAR(100),
        sessionDuration INT,
        location NVARCHAR(100),
        FOREIGN KEY (adminId) REFERENCES admin_users(id)
    );
    PRINT 'تم إنشاء جدول admin_login_log';
END

-- جدول الأدوار
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='admin_roles' AND xtype='U')
BEGIN
    CREATE TABLE admin_roles (
        id INT IDENTITY(1,1) PRIMARY KEY,
        roleName NVARCHAR(30) UNIQUE NOT NULL,
        roleNameAr NVARCHAR(30) NOT NULL,
        description NVARCHAR(200),
        descriptionAr NVARCHAR(200),
        permissions NVARCHAR(MAX),
        isActive BIT NOT NULL DEFAULT 1,
        createdDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        createdBy INT,
        FOREIGN KEY (createdBy) REFERENCES admin_users(id)
    );
    PRINT 'تم إنشاء جدول admin_roles';
END

-- إدراج البيانات الأساسية إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM admin_permissions)
BEGIN
    -- إدراج الصلاحيات الأساسية
    INSERT INTO admin_permissions (permissionName, permissionNameAr, description, descriptionAr, category) VALUES
    ('SYSTEM_VIEW', 'عرض النظام', 'View system information', 'عرض معلومات النظام', 'System'),
    ('SYSTEM_MANAGE', 'إدارة النظام', 'Manage system settings', 'إدارة إعدادات النظام', 'System'),
    ('PHARMACY_VIEW', 'عرض الصيدليات', 'View pharmacies', 'عرض الصيدليات', 'Pharmacy'),
    ('PHARMACY_ADD', 'إضافة صيدلية', 'Add pharmacy', 'إضافة صيدلية', 'Pharmacy'),
    ('PHARMACY_EDIT', 'تعديل صيدلية', 'Edit pharmacy', 'تعديل صيدلية', 'Pharmacy'),
    ('PHARMACY_DELETE', 'حذف صيدلية', 'Delete pharmacy', 'حذف صيدلية', 'Pharmacy'),
    ('PHARMACY_APPROVE', 'موافقة الصيدليات', 'Approve pharmacies', 'الموافقة على الصيدليات', 'Pharmacy'),
    ('USER_VIEW', 'عرض المستخدمين', 'View users', 'عرض المستخدمين', 'User'),
    ('USER_CREATE', 'إنشاء مستخدم', 'Create user', 'إنشاء مستخدم', 'User'),
    ('USER_EDIT', 'تعديل مستخدم', 'Edit user', 'تعديل مستخدم', 'User'),
    ('USER_DELETE', 'حذف مستخدم', 'Delete user', 'حذف مستخدم', 'User'),
    ('USER_PERMISSIONS', 'إدارة الصلاحيات', 'Manage permissions', 'إدارة الصلاحيات', 'User'),
    ('REPORT_VIEW', 'عرض التقارير', 'View reports', 'عرض التقارير', 'Report'),
    ('REPORT_CREATE', 'إنشاء التقارير', 'Create reports', 'إنشاء التقارير', 'Report'),
    ('BACKUP_VIEW', 'عرض النسخ الاحتياطية', 'View backups', 'عرض النسخ الاحتياطية', 'Backup'),
    ('BACKUP_CREATE', 'إنشاء نسخة احتياطية', 'Create backup', 'إنشاء نسخة احتياطية', 'Backup');

    PRINT 'تم إدراج الصلاحيات الأساسية';
END

-- إدراج الأدوار الأساسية
IF NOT EXISTS (SELECT * FROM admin_roles)
BEGIN
    INSERT INTO admin_roles (roleName, roleNameAr, description, descriptionAr, permissions) VALUES
    ('SuperAdmin', 'مدير عام', 'Full system access', 'وصول كامل للنظام', '[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]'),
    ('Admin', 'مدير', 'Administrative access', 'وصول إداري', '[1,3,4,5,6,7,8,9,10,11,13,14,15,16]'),
    ('Moderator', 'مشرف', 'Limited administrative access', 'وصول إداري محدود', '[1,3,4,5,8,13,14]'),
    ('Viewer', 'مراقب', 'Read-only access', 'وصول للقراءة فقط', '[1,3,8,13]');

    PRINT 'تم إدراج الأدوار الأساسية';
END

-- إدراج المدير الافتراضي إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM admin_users WHERE username = 'admin')
BEGIN
    INSERT INTO admin_users (username, password, fullName, fullNameAr, email, phone, role, isActive, createdDate)
    VALUES ('admin', 'Admin@123', 'System Administrator', 'مدير النظام', '<EMAIL>', '**********', 'SuperAdmin', 1, GETDATE());

    -- منح جميع الصلاحيات للمدير الافتراضي
    DECLARE @adminId INT = SCOPE_IDENTITY();
    INSERT INTO admin_user_permissions (adminId, permissionId, grantedBy)
    SELECT @adminId, id, @adminId FROM admin_permissions WHERE isActive = 1;

    PRINT 'تم إنشاء المدير الافتراضي';
    PRINT 'اسم المستخدم: admin';
    PRINT 'كلمة المرور: Admin@123';
END

-- =============================================
-- إعداد قاعدة بيانات الصيدلية
-- =============================================
USE pharmacy;
GO

-- جدول الصيدليات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyName NVARCHAR(100) NOT NULL,
        pharmacyNameAr NVARCHAR(100) NOT NULL,
        ownerName NVARCHAR(100) NOT NULL,
        ownerNameAr NVARCHAR(100) NOT NULL,
        licenseNumber NVARCHAR(50) UNIQUE NOT NULL,
        taxNumber NVARCHAR(50),
        email NVARCHAR(100) UNIQUE NOT NULL,
        phone NVARCHAR(20) NOT NULL,
        mobile NVARCHAR(20),
        address NVARCHAR(200) NOT NULL,
        addressAr NVARCHAR(200) NOT NULL,
        city NVARCHAR(50) NOT NULL,
        cityAr NVARCHAR(50) NOT NULL,
        region NVARCHAR(50),
        regionAr NVARCHAR(50),
        postalCode NVARCHAR(10),
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        status NVARCHAR(20) NOT NULL DEFAULT 'Pending',
        isActive BIT NOT NULL DEFAULT 1,
        registrationDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        approvalDate DATETIME2,
        approvedBy INT,
        lastActivityDate DATETIME2,
        subscriptionStatus NVARCHAR(20) NOT NULL DEFAULT 'Trial',
        notes NVARCHAR(300)
    );
    PRINT 'تم إنشاء جدول pharmacies';
END

-- إدراج صيدليات تجريبية
IF NOT EXISTS (SELECT * FROM pharmacies WHERE licenseNumber = 'LIC001')
BEGIN
    INSERT INTO pharmacies (
        pharmacyName, pharmacyNameAr, ownerName, ownerNameAr, licenseNumber, taxNumber,
        email, phone, mobile, address, addressAr, city, cityAr, region, regionAr,
        postalCode, latitude, longitude, status, subscriptionStatus, notes
    ) VALUES
    (
        'Al-Noor Pharmacy', 'صيدلية النور', 'Ahmed Mohammed Ali', 'أحمد محمد علي', 'LIC001', 'TAX001',
        '<EMAIL>', '**********', '**********', 'King Fahd Road, Al-Noor District', 'شارع الملك فهد، حي النور',
        'Riyadh', 'الرياض', 'Riyadh', 'الرياض', '12345', 24.7136, 46.6753, 'Approved', 'Premium', 'صيدلية رئيسية في حي النور'
    ),
    (
        'Al-Shifa Pharmacy', 'صيدلية الشفاء', 'Fatima Ahmed', 'فاطمة أحمد', 'LIC002', 'TAX002',
        '<EMAIL>', '**********', '**********', 'King Abdulaziz Road, Al-Malaz', 'طريق الملك عبدالعزيز، حي الملز',
        'Riyadh', 'الرياض', 'Riyadh', 'الرياض', '12346', 24.6408, 46.7728, 'Approved', 'Basic', 'صيدلية في حي الملز'
    ),
    (
        'Al-Hayat Pharmacy', 'صيدلية الحياة', 'Mohammed Salem', 'محمد سالم', 'LIC003', 'TAX003',
        '<EMAIL>', '0122345678', '0501234569', 'Tahlia Street, Al-Salamah', 'شارع التحلية، حي السلامة',
        'Jeddah', 'جدة', 'Makkah', 'مكة المكرمة', '21589', 21.5810, 39.1653, 'Pending', 'Trial', 'صيدلية جديدة في جدة'
    );

    PRINT 'تم إدراج 3 صيدليات تجريبية';
END

PRINT '=============================================';
PRINT 'تم إعداد قواعد البيانات بنجاح!';
PRINT 'يمكنك الآن تشغيل التطبيق';
PRINT '=============================================';
PRINT 'بيانات تسجيل الدخول:';
PRINT 'اسم المستخدم: admin';
PRINT 'كلمة المرور: Admin@123';
PRINT '=============================================';
