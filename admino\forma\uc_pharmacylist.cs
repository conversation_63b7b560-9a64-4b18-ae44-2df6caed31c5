﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace admino.forma
{
    public partial class uc_pharmacylist : UserControl
    {
        private DataTable pharmaciesData;

        public uc_pharmacylist()
        {
            InitializeComponent();
            InitializeCustomComponents();
            LoadPharmacies();
        }

        private void InitializeCustomComponents()
        {
            // Set right-to-left for Arabic support
            this.RightToLeft = RightToLeft.Yes;

            // Configure DataGridView
            ConfigureDataGridView();

            // Ensure events are connected (remove existing first to avoid duplicates)
            dgvPharmacies.CellClick -= dgvPharmacies_CellClick;
            dgvPharmacies.CellClick += dgvPharmacies_CellClick;
            dgvPharmacies.CellDoubleClick -= dgvPharmacies_CellDoubleClick;
            dgvPharmacies.CellDoubleClick += dgvPharmacies_CellDoubleClick;

            // Setup full screen layout
            SetupFullScreenLayout();

            // Initialize filters
            InitializeFilters();
        }

        private void SetupFullScreenLayout()
        {
            // Ensure the control fills the parent
            this.Dock = DockStyle.Fill;

            // Set minimum size
            this.MinimumSize = new Size(1200, 800);

            // Configure panels for responsive design
            guna2Panel1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            guna2Panel2.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            guna2Panel3.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;

            // Ensure DataGridView fills its panel
            dgvPharmacies.Dock = DockStyle.Fill;

            // Add resize event handler
            this.Resize += Uc_pharmacylist_Resize;

            // Apply initial layout
            this.Load += Uc_pharmacylist_Load;
        }

        private void Uc_pharmacylist_Load(object sender, EventArgs e)
        {
            // Apply proper layout on load
            AdjustLayoutForCurrentSize();
        }

        private void AdjustLayoutForCurrentSize()
        {
            // Get parent form size or use default
            int formWidth = this.Parent?.Width ?? 1920;
            int formHeight = this.Parent?.Height ?? 1080;

            // Set control size to match parent
            this.Size = new Size(formWidth, formHeight);

            // Calculate proper positions and sizes
            int panelWidth = formWidth - 40;

            // Update panel widths
            guna2Panel1.Width = panelWidth;
            guna2Panel2.Width = panelWidth;
            guna2Panel3.Width = panelWidth;

            // Calculate heights
            int availableHeight = formHeight - 250; // Leave space for title, search, and stats
            if (availableHeight > 400)
            {
                guna2Panel2.Height = availableHeight;
            }

            // Position statistics panel at bottom
            guna2Panel3.Top = formHeight - 100;

            // Distribute statistics labels
            if (panelWidth > 800)
            {
                lblTotalPharmacies.Left = 50;
                lblActivePharmacies.Left = panelWidth / 2 - 100;
                lblInactivePharmacies.Left = panelWidth - 250;
            }
        }

        private void Uc_pharmacylist_Resize(object sender, EventArgs e)
        {
            // Adjust layout when window is resized
            if (this.Width > 0 && this.Height > 0)
            {
                // Update panel sizes based on current form size
                int panelWidth = this.Width - 40; // 20px margin on each side

                guna2Panel1.Width = panelWidth;
                guna2Panel2.Width = panelWidth;
                guna2Panel3.Width = panelWidth;

                // Calculate proper heights
                int titleHeight = 60;  // Title area
                int searchHeight = 100; // Search panel + margins
                int statsHeight = 100;  // Statistics panel + margins
                int margins = 40;       // Additional margins

                // Update DataGridView height to fit properly
                int availableHeight = this.Height - titleHeight - searchHeight - statsHeight - margins;
                if (availableHeight > 300)
                {
                    guna2Panel2.Height = availableHeight;
                    guna2Panel2.Top = titleHeight + searchHeight;
                }

                // Update statistics panel position to be at bottom
                guna2Panel3.Top = this.Height - statsHeight;

                // Update search controls positions
                int searchWidth = Math.Min(600, panelWidth - 250);
                txtSearch.Width = searchWidth;
                btnSearch.Left = txtSearch.Right + 20;
                btnRefresh.Left = btnSearch.Right + 20;

                // Update statistics labels positions for better distribution
                if (panelWidth > 800)
                {
                    lblTotalPharmacies.Left = 50;
                    lblActivePharmacies.Left = panelWidth / 2 - 100;
                    lblInactivePharmacies.Left = panelWidth - 250;
                }
            }
        }

        private void InitializeFilters()
        {
            // Initialize Status Filter
            cmbStatusFilter.Items.Clear();
            cmbStatusFilter.Items.Add("الكل");
            cmbStatusFilter.Items.Add("نشط (Approved)");
            cmbStatusFilter.Items.Add("معلق (Pending)");
            cmbStatusFilter.Items.Add("مرفوض (Rejected)");
            cmbStatusFilter.Items.Add("موقوف (Suspended)");
            cmbStatusFilter.SelectedIndex = 0;

            // Initialize Subscription Filter
            cmbSubscriptionFilter.Items.Clear();
            cmbSubscriptionFilter.Items.Add("الكل");
            cmbSubscriptionFilter.Items.Add("تجريبي (Trial)");
            cmbSubscriptionFilter.Items.Add("أساسي (Basic)");
            cmbSubscriptionFilter.Items.Add("متقدم (Premium)");
            cmbSubscriptionFilter.Items.Add("مؤسسي (Enterprise)");
            cmbSubscriptionFilter.Items.Add("منتهي الصلاحية");
            cmbSubscriptionFilter.Items.Add("ساري المفعول");
            cmbSubscriptionFilter.SelectedIndex = 0;
        }

        private void cmbStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void cmbSubscriptionFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void btnClearFilters_Click(object sender, EventArgs e)
        {
            // Reset all filters
            cmbStatusFilter.SelectedIndex = 0;
            cmbSubscriptionFilter.SelectedIndex = 0;
            txtSearch.Text = "";

            // Reload all data
            LoadPharmacies();
        }

        private void ApplyFilters()
        {
            if (pharmaciesData == null) return;

            DataView dv = new DataView(pharmaciesData);
            string filter = "1=1"; // Base condition

            // Apply Status Filter
            if (cmbStatusFilter.SelectedIndex > 0)
            {
                string statusFilter = "";
                switch (cmbStatusFilter.SelectedIndex)
                {
                    case 1: // نشط (Approved)
                        statusFilter = "Status = 'Approved'";
                        break;
                    case 2: // معلق (Pending)
                        statusFilter = "Status = 'Pending'";
                        break;
                    case 3: // مرفوض (Rejected)
                        statusFilter = "Status = 'Rejected'";
                        break;
                    case 4: // موقوف (Suspended)
                        statusFilter = "Status = 'Suspended'";
                        break;
                }
                if (!string.IsNullOrEmpty(statusFilter))
                {
                    filter += " AND " + statusFilter;
                }
            }

            // Apply Subscription Filter
            if (cmbSubscriptionFilter.SelectedIndex > 0)
            {
                string subscriptionFilter = "";
                switch (cmbSubscriptionFilter.SelectedIndex)
                {
                    case 1: // تجريبي (Trial)
                        subscriptionFilter = "SubscriptionStatus = 'Trial'";
                        break;
                    case 2: // أساسي (Basic)
                        subscriptionFilter = "SubscriptionStatus = 'Basic'";
                        break;
                    case 3: // متقدم (Premium)
                        subscriptionFilter = "SubscriptionStatus = 'Premium'";
                        break;
                    case 4: // مؤسسي (Enterprise)
                        subscriptionFilter = "SubscriptionStatus = 'Enterprise'";
                        break;
                    case 5: // منتهي الصلاحية
                        subscriptionFilter = "SubscriptionEndDate < #" + DateTime.Now.ToString("yyyy-MM-dd") + "#";
                        break;
                    case 6: // ساري المفعول
                        subscriptionFilter = "SubscriptionEndDate >= #" + DateTime.Now.ToString("yyyy-MM-dd") + "# OR SubscriptionEndDate IS NULL";
                        break;
                }
                if (!string.IsNullOrEmpty(subscriptionFilter))
                {
                    filter += " AND " + subscriptionFilter;
                }
            }

            // Apply search filter if exists
            if (!string.IsNullOrEmpty(txtSearch.Text.Trim()))
            {
                string searchText = txtSearch.Text.Trim();
                string searchFilter = $"(PharmacyName LIKE '%{searchText}%' OR " +
                                    $"PharmacyNameAr LIKE '%{searchText}%' OR " +
                                    $"OwnerName LIKE '%{searchText}%' OR " +
                                    $"OwnerNameAr LIKE '%{searchText}%' OR " +
                                    $"LicenseNumber LIKE '%{searchText}%' OR " +
                                    $"City LIKE '%{searchText}%' OR " +
                                    $"CityAr LIKE '%{searchText}%')";
                filter += " AND " + searchFilter;
            }

            try
            {
                dv.RowFilter = filter;
                dgvPharmacies.DataSource = dv;
                UpdateStatistics(dv.ToTable());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلتر: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                // Fallback to original data
                dgvPharmacies.DataSource = pharmaciesData;
                UpdateStatistics(pharmaciesData);
            }
        }

        private void ConfigureDataGridView()
        {
            // Set DataGridView properties
            dgvPharmacies.AutoGenerateColumns = false;
            dgvPharmacies.AllowUserToAddRows = false;
            dgvPharmacies.AllowUserToDeleteRows = false;
            dgvPharmacies.ReadOnly = true;
            dgvPharmacies.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvPharmacies.MultiSelect = false;
            dgvPharmacies.RightToLeft = RightToLeft.Yes;

            // Add columns
            dgvPharmacies.Columns.Clear();

            dgvPharmacies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                DataPropertyName = "id",
                HeaderText = "الرقم",
                Width = 60,
                Visible = false
            });

            dgvPharmacies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PharmacyNameAr",
                DataPropertyName = "pharmacyNameAr",
                HeaderText = "اسم الصيدلية",
                Width = 200
            });

            dgvPharmacies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "OwnerNameAr",
                DataPropertyName = "ownerNameAr",
                HeaderText = "اسم المالك",
                Width = 150
            });

            dgvPharmacies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LicenseNumber",
                DataPropertyName = "licenseNumber",
                HeaderText = "رقم الترخيص",
                Width = 120
            });

            dgvPharmacies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CityAr",
                DataPropertyName = "cityAr",
                HeaderText = "المدينة",
                Width = 100
            });

            dgvPharmacies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                DataPropertyName = "phone",
                HeaderText = "الهاتف",
                Width = 120
            });

            dgvPharmacies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                DataPropertyName = "email",
                HeaderText = "البريد الإلكتروني",
                Width = 150
            });

            dgvPharmacies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                DataPropertyName = "status",
                HeaderText = "حالة الطلب",
                Width = 100
            });

            dgvPharmacies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "SubscriptionStatus",
                DataPropertyName = "subscriptionStatus",
                HeaderText = "حالة الاشتراك",
                Width = 120
            });

            // Status column with custom formatting
            DataGridViewTextBoxColumn statusColumn = new DataGridViewTextBoxColumn
            {
                Name = "Status",
                DataPropertyName = "isActive",
                HeaderText = "الحالة",
                Width = 80
            };
            dgvPharmacies.Columns.Add(statusColumn);

            dgvPharmacies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RegistrationDate",
                DataPropertyName = "registrationDate",
                HeaderText = "تاريخ التسجيل",
                Width = 120
            });

            // Add action buttons column
            DataGridViewButtonColumn actionColumn = new DataGridViewButtonColumn
            {
                Name = "Actions",
                HeaderText = "الإجراءات",
                Text = "تعديل",
                UseColumnTextForButtonValue = true,
                Width = 80
            };
            dgvPharmacies.Columns.Add(actionColumn);
        }

        private void ConfigureDataGridViewColumns()
        {
            // Configure column headers and widths
            if (dgvPharmacies.Columns.Contains("id"))
                dgvPharmacies.Columns["id"].HeaderText = "المعرف";

            if (dgvPharmacies.Columns.Contains("pharmacyName"))
                dgvPharmacies.Columns["pharmacyName"].HeaderText = "اسم الصيدلية";

            if (dgvPharmacies.Columns.Contains("pharmacyNameAr"))
                dgvPharmacies.Columns["pharmacyNameAr"].HeaderText = "اسم الصيدلية (عربي)";

            if (dgvPharmacies.Columns.Contains("ownerName"))
                dgvPharmacies.Columns["ownerName"].HeaderText = "اسم المالك";

            if (dgvPharmacies.Columns.Contains("licenseNumber"))
                dgvPharmacies.Columns["licenseNumber"].HeaderText = "رقم الترخيص";

            if (dgvPharmacies.Columns.Contains("email"))
                dgvPharmacies.Columns["email"].HeaderText = "البريد الإلكتروني";

            if (dgvPharmacies.Columns.Contains("phone"))
                dgvPharmacies.Columns["phone"].HeaderText = "الهاتف";

            if (dgvPharmacies.Columns.Contains("city"))
                dgvPharmacies.Columns["city"].HeaderText = "المدينة";

            if (dgvPharmacies.Columns.Contains("status"))
                dgvPharmacies.Columns["status"].HeaderText = "الحالة";

            // Add action column if it doesn't exist
            if (!dgvPharmacies.Columns.Contains("Actions"))
            {
                DataGridViewButtonColumn actionColumn = new DataGridViewButtonColumn
                {
                    Name = "Actions",
                    HeaderText = "الإجراءات",
                    Text = "تعديل",
                    UseColumnTextForButtonValue = true,
                    Width = 80
                };
                dgvPharmacies.Columns.Add(actionColumn);
            }
        }

        private void LoadPharmacies()
        {
            try
            {
                // Test database connection first
                if (!DatabaseConnection.TestPharmacyConnection())
                {
                    MessageBox.Show("خطأ في الاتصال بقاعدة بيانات الصيدلية.\n\n" +
                                  "تحقق من:\n" +
                                  "1. تشغيل SQL Server\n" +
                                  "2. وجود قاعدة البيانات 'UnifiedPharmacy'\n" +
                                  "3. تنفيذ ملف Database_Setup.sql\n" +
                                  "4. تنفيذ ملف Fix_UnifiedPharmacy_Columns.sql",
                                  "خطأ في الاتصال", MessageBoxButtons.OK, MessageBoxIcon.Error);

                    lblTotalPharmacies.Text = "خطأ في الاتصال";
                    lblActivePharmacies.Text = "";
                    lblInactivePharmacies.Text = "";
                    return;
                }

                // Show loading message
                lblTotalPharmacies.Text = "جاري تحميل البيانات...";
                lblActivePharmacies.Text = "";
                lblInactivePharmacies.Text = "";

                pharmaciesData = PharmacyManager.GetAllPharmacies();
                dgvPharmacies.DataSource = pharmaciesData;

                // Re-configure DataGridView after setting data source
                ConfigureDataGridViewColumns();

                // Update status display
                UpdateStatusDisplay();

                // Update statistics
                UpdateStatistics();

                // Show success message if no data
                if (pharmaciesData.Rows.Count == 0)
                {
                    MessageBox.Show("لا توجد صيدليات مسجلة في قاعدة البيانات\n" +
                                  "يمكنك إضافة صيدلية جديدة من قائمة 'إضافة صيدلية'\n\n" +
                                  "إذا كنت تتوقع وجود بيانات، تحقق من:\n" +
                                  "1. تنفيذ ملف Database_Setup.sql كاملاً\n" +
                                  "2. إدراج البيانات التجريبية",
                                  "لا توجد بيانات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Show data source confirmation
                    lblTotalPharmacies.Text += " (من قاعدة البيانات)";
                }
            }
            catch (Exception ex)
            {
                string errorMessage = $"خطأ في تحميل بيانات الصيدليات: {ex.Message}\n\n";

                if (ex.Message.Contains("Invalid column name"))
                {
                    errorMessage += "المشكلة: أعمدة مفقودة في جدول الصيدليات\n" +
                                  "الحل: نفذ ملف Fix_Database_Columns.sql لإصلاح بنية الجدول";
                }
                else if (ex.Message.Contains("Invalid object name 'pharmacies'"))
                {
                    errorMessage += "المشكلة: جدول الصيدليات غير موجود\n" +
                                  "الحل: نفذ ملف Database_Setup.sql لإنشاء الجداول";
                }
                else if (ex.Message.Contains("Cannot open database") || ex.Message.Contains("server was not found"))
                {
                    errorMessage += "المشكلة: خطأ في الاتصال بقاعدة البيانات\n" +
                                  "الحل: تحقق من تشغيل SQL Server ووجود قاعدة البيانات 'UnifiedPharmacy'";
                }
                else
                {
                    errorMessage += "تحقق من:\n" +
                                  "1. الاتصال بقاعدة البيانات\n" +
                                  "2. وجود جدول pharmacies\n" +
                                  "3. صلاحيات الوصول\n" +
                                  "4. تنفيذ ملفات الإعداد";
                }

                MessageBox.Show(errorMessage, "خطأ في قاعدة البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Reset labels
                lblTotalPharmacies.Text = "خطأ في التحميل";
                lblActivePharmacies.Text = "";
                lblInactivePharmacies.Text = "";
            }
        }

        private void UpdateStatusDisplay()
        {
            foreach (DataGridViewRow row in dgvPharmacies.Rows)
            {
                if (row.Cells["Status"].Value != null)
                {
                    bool isActive = Convert.ToBoolean(row.Cells["Status"].Value);
                    row.Cells["Status"].Value = isActive ? "نشط" : "غير نشط";

                    // Color coding
                    if (isActive)
                    {
                        row.Cells["Status"].Style.ForeColor = Color.Green;
                        row.Cells["Status"].Style.Font = new Font(dgvPharmacies.Font, FontStyle.Bold);
                    }
                    else
                    {
                        row.Cells["Status"].Style.ForeColor = Color.Red;
                        row.Cells["Status"].Style.Font = new Font(dgvPharmacies.Font, FontStyle.Bold);
                    }
                }
            }
        }

        private void UpdateStatistics()
        {
            UpdateStatistics(pharmaciesData);
        }

        private void UpdateStatistics(DataTable data)
        {
            if (data != null)
            {
                int totalPharmacies = data.Rows.Count;
                int activePharmacies = 0;
                int inactivePharmacies = 0;

                try
                {
                    // Count active pharmacies based on Status field
                    activePharmacies = data.AsEnumerable()
                        .Count(row => row["Status"].ToString() == "Approved");
                    inactivePharmacies = totalPharmacies - activePharmacies;
                }
                catch
                {
                    // Fallback to isActive field if Status field doesn't exist
                    try
                    {
                        activePharmacies = data.AsEnumerable()
                            .Count(row => Convert.ToBoolean(row["isActive"]));
                        inactivePharmacies = totalPharmacies - activePharmacies;
                    }
                    catch
                    {
                        // If both fields fail, show totals only
                        activePharmacies = 0;
                        inactivePharmacies = 0;
                    }
                }

                lblTotalPharmacies.Text = $"إجمالي الصيدليات: {totalPharmacies}";
                lblActivePharmacies.Text = $"الصيدليات النشطة: {activePharmacies}";
                lblInactivePharmacies.Text = $"الصيدليات غير النشطة: {inactivePharmacies}";
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            // Use the unified filter system
            ApplyFilters();
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadPharmacies();
            txtSearch.Clear();
        }

        private void dgvPharmacies_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
                {
                    // Check if Actions column exists
                    if (dgvPharmacies.Columns.Contains("Actions"))
                    {
                        // Handle action button click
                        if (e.ColumnIndex == dgvPharmacies.Columns["Actions"].Index)
                        {
                            // Get pharmacy ID
                            var idCell = dgvPharmacies.Rows[e.RowIndex].Cells["id"];
                            if (idCell?.Value != null)
                            {
                                int pharmacyId = Convert.ToInt32(idCell.Value);
                                EditPharmacy(pharmacyId);
                            }
                            else
                            {
                                MessageBox.Show("لا يمكن تحديد معرف الصيدلية", "خطأ",
                                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            }
                        }
                    }
                    else
                    {
                        // If Actions column doesn't exist, show context menu or handle double-click
                        MessageBox.Show("عمود الإجراءات غير موجود. جاري إعادة تكوين الجدول...", "تنبيه",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                        ConfigureDataGridViewColumns();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة النقر: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditPharmacy(int pharmacyId)
        {
            try
            {
                // Show loading message
                this.Cursor = Cursors.WaitCursor;

                PharmacyInfo pharmacy = PharmacyManager.GetPharmacyById(pharmacyId);
                if (pharmacy != null)
                {
                    // Create edit form
                    add_pharmacy editForm = new add_pharmacy(pharmacy);

                    // Create edit window
                    Form editWindow = new Form
                    {
                        Text = $"تعديل بيانات الصيدلية - {pharmacy.PharmacyNameAr}",
                        Size = new Size(1400, 900),
                        StartPosition = FormStartPosition.CenterParent,
                        WindowState = FormWindowState.Maximized,
                        MinimumSize = new Size(1200, 800),
                        Icon = this.FindForm()?.Icon // Use same icon as parent
                    };

                    editForm.Dock = DockStyle.Fill;
                    editWindow.Controls.Add(editForm);

                    this.Cursor = Cursors.Default;

                    // Show edit window
                    DialogResult result = editWindow.ShowDialog(this.FindForm());

                    if (result == DialogResult.OK)
                    {
                        // Refresh the list after successful edit
                        LoadPharmacies();
                        MessageBox.Show("تم تحديث بيانات الصيدلية بنجاح", "تم التحديث",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    this.Cursor = Cursors.Default;
                    MessageBox.Show($"لا يمكن العثور على الصيدلية بالمعرف: {pharmacyId}", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                string errorMessage = $"خطأ في تحميل بيانات الصيدلية: {ex.Message}";

                if (ex.Message.Contains("Cannot open database"))
                {
                    errorMessage += "\n\nتحقق من:\n1. تشغيل SQL Server\n2. وجود قاعدة البيانات UnifiedPharmacy";
                }

                MessageBox.Show(errorMessage, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dgvPharmacies_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0)
                {
                    // Get pharmacy ID from the row
                    var idCell = dgvPharmacies.Rows[e.RowIndex].Cells["id"];
                    if (idCell?.Value != null)
                    {
                        int pharmacyId = Convert.ToInt32(idCell.Value);
                        ShowPharmacyDetails(pharmacyId);
                    }
                    else
                    {
                        MessageBox.Show("لا يمكن تحديد معرف الصيدلية", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة النقر المزدوج: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowPharmacyDetails(int pharmacyId)
        {
            try
            {
                PharmacyInfo pharmacy = PharmacyManager.GetPharmacyById(pharmacyId);
                if (pharmacy != null)
                {
                    string details = $@"
تفاصيل الصيدلية:

اسم الصيدلية: {pharmacy.PharmacyNameAr} ({pharmacy.PharmacyName})
اسم المالك: {pharmacy.OwnerNameAr} ({pharmacy.OwnerName})
رقم الترخيص: {pharmacy.LicenseNumber}
الرقم الضريبي: {pharmacy.TaxNumber ?? "غير محدد"}
البريد الإلكتروني: {pharmacy.Email}
الهاتف: {pharmacy.Phone}
الجوال: {pharmacy.Mobile ?? "غير محدد"}

العنوان:
{pharmacy.AddressAr}
{pharmacy.Address}
المدينة: {pharmacy.CityAr} ({pharmacy.City})
المنطقة: {pharmacy.RegionAr ?? "غير محدد"} ({pharmacy.Region ?? "غير محدد"})
الرمز البريدي: {pharmacy.PostalCode ?? "غير محدد"}

معلومات الحالة:
حالة الطلب: {pharmacy.Status}
حالة الاشتراك: {pharmacy.SubscriptionStatus}
نشط: {(pharmacy.IsActive ? "نعم" : "لا")}

التواريخ:
تاريخ التسجيل: {pharmacy.RegistrationDate.ToString("dd/MM/yyyy")}
تاريخ الموافقة: {pharmacy.ApprovalDate?.ToString("dd/MM/yyyy") ?? "لم تتم الموافقة بعد"}
آخر نشاط: {pharmacy.LastActivityDate?.ToString("dd/MM/yyyy HH:mm") ?? "لا يوجد نشاط"}

ملاحظات: {pharmacy.Notes ?? "لا توجد ملاحظات"}";

                    MessageBox.Show(details, "تفاصيل الصيدلية", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض تفاصيل الصيدلية: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void txtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnSearch_Click(sender, e);
            }
        }
    }
}
