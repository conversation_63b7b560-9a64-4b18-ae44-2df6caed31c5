-- =============================================
-- إصلاح شامل لقاعدة البيانات UnifiedPharmacy
-- Complete Fix for UnifiedPharmacy Database
-- =============================================

-- التأكد من استخدام قاعدة البيانات الصحيحة
USE UnifiedPharmacy;
GO

PRINT 'بدء الإصلاح الشامل لقاعدة البيانات UnifiedPharmacy...';

-- حذف الجدول الموجود وإعادة إنشاؤه بالبنية الصحيحة
IF EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    -- نسخ احتياطية من البيانات الموجودة
    IF EXISTS (SELECT * FROM pharmacies)
    BEGIN
        SELECT * INTO pharmacies_backup FROM pharmacies;
        PRINT 'تم إنشاء نسخة احتياطية من البيانات الموجودة في جدول pharmacies_backup';
    END
    
    DROP TABLE pharmacies;
    PRINT 'تم حذف الجدول القديم';
END

-- إنشاء الجدول بالبنية الصحيحة والكاملة
CREATE TABLE pharmacies (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyCode NVARCHAR(20) UNIQUE NOT NULL,
    pharmacyName NVARCHAR(100) NOT NULL,
    pharmacyNameAr NVARCHAR(100) NOT NULL,
    ownerName NVARCHAR(100) NOT NULL,
    ownerNameAr NVARCHAR(100) NOT NULL,
    licenseNumber NVARCHAR(50) UNIQUE NOT NULL,
    taxNumber NVARCHAR(50),
    email NVARCHAR(100) UNIQUE NOT NULL,
    phone NVARCHAR(20) NOT NULL,
    mobile NVARCHAR(20),
    address NVARCHAR(200) NOT NULL,
    addressAr NVARCHAR(200) NOT NULL,
    city NVARCHAR(50) NOT NULL,
    cityAr NVARCHAR(50) NOT NULL,
    region NVARCHAR(50),
    regionAr NVARCHAR(50),
    postalCode NVARCHAR(10),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    status NVARCHAR(20) NOT NULL DEFAULT 'Pending',
    isActive BIT NOT NULL DEFAULT 1,
    registrationDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    approvalDate DATETIME2,
    approvedBy INT,
    lastActivityDate DATETIME2,
    subscriptionStatus NVARCHAR(20) NOT NULL DEFAULT 'Trial',
    subscriptionStartDate DATETIME2 DEFAULT GETDATE(),
    subscriptionEndDate DATETIME2,
    notes NVARCHAR(300)
);

PRINT 'تم إنشاء جدول pharmacies بالبنية الصحيحة';

-- استعادة البيانات من النسخة الاحتياطية إذا كانت موجودة
IF EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies_backup' AND xtype='U')
BEGIN
    INSERT INTO pharmacies (
        pharmacyCode, pharmacyName, pharmacyNameAr, ownerName, ownerNameAr, licenseNumber,
        email, phone, address, addressAr, city, cityAr,
        latitude, longitude, status, isActive, registrationDate
    )
    SELECT
        'PH2024' + RIGHT('000' + CAST(id as NVARCHAR), 3),
        ISNULL(pharmacyName, 'Unknown'),
        ISNULL(pharmacyNameAr, 'غير محدد'),
        ISNULL(ownerName, 'Unknown'),
        ISNULL(ownerNameAr, 'غير محدد'),
        ISNULL(licenseNumber, 'LIC' + CAST(id as NVARCHAR)),
        ISNULL(email, 'pharmacy' + CAST(id as NVARCHAR) + '@example.com'),
        ISNULL(phone, '**********'),
        ISNULL(address, 'Unknown Address'),
        ISNULL(addressAr, 'عنوان غير محدد'),
        ISNULL(city, 'Unknown'),
        ISNULL(cityAr, 'غير محدد'),
        latitude,
        longitude,
        ISNULL(status, 'Pending'),
        ISNULL(isActive, 1),
        ISNULL(registrationDate, GETDATE())
    FROM pharmacies_backup;
    
    PRINT 'تم استعادة البيانات من النسخة الاحتياطية';
    
    -- حذف النسخة الاحتياطية
    DROP TABLE pharmacies_backup;
    PRINT 'تم حذف النسخة الاحتياطية';
END

-- إدراج بيانات تجريبية إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM pharmacies)
BEGIN
    INSERT INTO pharmacies (
        pharmacyCode, pharmacyName, pharmacyNameAr, ownerName, ownerNameAr, licenseNumber, taxNumber,
        email, phone, mobile, address, addressAr, city, cityAr, region, regionAr,
        postalCode, latitude, longitude, status, subscriptionStatus, notes
    ) VALUES
    (
        'الرصي24001', 'Al-Noor Pharmacy', 'صيدلية النور', 'Ahmed Mohammed Ali', 'أحمد محمد علي', 'LIC001', 'TAX001',
        '<EMAIL>', '**********', '**********', 'King Fahd Road, Al-Noor District', 'شارع الملك فهد، حي النور',
        'Riyadh', 'الرياض', 'Riyadh', 'الرياض', '12345', 24.7136, 46.6753, 'Approved', 'Premium', 'صيدلية رئيسية في حي النور'
    ),
    (
        'الرصي24002', 'Al-Shifa Pharmacy', 'صيدلية الشفاء', 'Fatima Ahmed', 'فاطمة أحمد', 'LIC002', 'TAX002',
        '<EMAIL>', '**********', '**********', 'King Abdulaziz Road, Al-Malaz District', 'طريق الملك عبدالعزيز، حي الملز',
        'Riyadh', 'الرياض', 'Riyadh', 'الرياض', '12346', 24.6408, 46.7728, 'Approved', 'Basic', 'صيدلية متخصصة في الأدوية العامة'
    ),
    (
        'جدصي24003', 'Al-Hayat Pharmacy', 'صيدلية الحياة', 'Mohammed Salem', 'محمد سالم', 'LIC003', 'TAX003',
        '<EMAIL>', '0122345678', '0501234569', 'Tahlia Street, Al-Salamah District', 'شارع التحلية، حي السلامة',
        'Jeddah', 'جدة', 'Makkah', 'مكة المكرمة', '21589', 21.5810, 39.1653, 'Pending', 'Trial', 'صيدلية حديثة في جدة'
    );
    
    PRINT 'تم إدراج البيانات التجريبية';
END

-- تحديث البيانات الموجودة بالقيم الافتراضية
UPDATE pharmacies 
SET 
    subscriptionStartDate = ISNULL(subscriptionStartDate, GETDATE()),
    subscriptionEndDate = ISNULL(subscriptionEndDate, DATEADD(MONTH, 1, GETDATE())),
    lastActivityDate = ISNULL(lastActivityDate, GETDATE()),
    taxNumber = ISNULL(taxNumber, ''),
    mobile = ISNULL(mobile, ''),
    region = ISNULL(region, ''),
    regionAr = ISNULL(regionAr, ''),
    postalCode = ISNULL(postalCode, ''),
    notes = ISNULL(notes, '')
WHERE id IS NOT NULL;

PRINT 'تم تحديث البيانات بالقيم الافتراضية';

-- عرض بنية الجدول المحدثة
PRINT 'بنية الجدول المحدثة:';
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'pharmacies'
ORDER BY ORDINAL_POSITION;

-- عرض عدد الصيدليات
DECLARE @count INT;
SELECT @count = COUNT(*) FROM pharmacies;
PRINT 'عدد الصيدليات في الجدول: ' + CAST(@count AS NVARCHAR);

-- اختبار إدراج صيدلية تجريبية
BEGIN TRY
    INSERT INTO pharmacies (
        pharmacyName, pharmacyNameAr, ownerName, ownerNameAr, 
        licenseNumber, email, phone, address, addressAr, city, cityAr
    ) VALUES (
        'Test Insert Pharmacy', 'صيدلية اختبار الإدراج', 'Test Owner', 'مالك الاختبار',
        'TEST_INSERT_001', '<EMAIL>', '**********', 
        'Test Address', 'عنوان الاختبار', 'Test City', 'مدينة الاختبار'
    );
    
    PRINT 'نجح اختبار الإدراج - يمكن إضافة صيدليات جديدة';
    
    -- حذف الصيدلية التجريبية
    DELETE FROM pharmacies WHERE licenseNumber = 'TEST_INSERT_001';
    PRINT 'تم حذف الصيدلية التجريبية';
    
END TRY
BEGIN CATCH
    PRINT 'فشل اختبار الإدراج: ' + ERROR_MESSAGE();
END CATCH

PRINT 'تم الانتهاء من الإصلاح الشامل لقاعدة البيانات UnifiedPharmacy بنجاح!';
PRINT 'يمكنك الآن استخدام النظام لإضافة وتعديل الصيدليات';
