using System;
using System.Data;
using System.Windows.Forms;

namespace admino
{
    public static class DatabaseTest
    {
        public static void TestConnections()
        {
            try
            {
                // Test Admin database connection
                bool adminConnected = DatabaseConnection.TestAdminConnection();
                MessageBox.Show($"اتصال قاعدة البيانات المركزية: {(adminConnected ? "نجح" : "فشل")}", 
                               "اختبار الاتصال", MessageBoxButtons.OK, 
                               adminConnected ? MessageBoxIcon.Information : MessageBoxIcon.Error);

                // Test Pharmacy database connection
                bool pharmacyConnected = DatabaseConnection.TestPharmacyConnection();
                MessageBox.Show($"اتصال قاعدة بيانات الصيدلية: {(pharmacyConnected ? "نجح" : "فشل")}", 
                               "اختبار الاتصال", MessageBoxButtons.OK, 
                               pharmacyConnected ? MessageBoxIcon.Information : MessageBoxIcon.Error);

                if (adminConnected && pharmacyConnected)
                {
                    TestDatabaseContent();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الاتصال: {ex.Message}", "خطأ", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void TestDatabaseContent()
        {
            try
            {
                // Test admin users table
                string adminQuery = "SELECT COUNT(*) FROM admin_users";
                DataTable adminResult = DatabaseConnection.ExecuteAdminQuery(adminQuery);
                int adminCount = Convert.ToInt32(adminResult.Rows[0][0]);

                // Test pharmacies table
                string pharmacyQuery = "SELECT COUNT(*) FROM pharmacies";
                DataTable pharmacyResult = DatabaseConnection.ExecutePharmacyQuery(pharmacyQuery);
                int pharmacyCount = Convert.ToInt32(pharmacyResult.Rows[0][0]);

                // Test permissions table
                string permissionsQuery = "SELECT COUNT(*) FROM admin_permissions";
                DataTable permissionsResult = DatabaseConnection.ExecuteAdminQuery(permissionsQuery);
                int permissionsCount = Convert.ToInt32(permissionsResult.Rows[0][0]);

                string message = $@"
محتويات قواعد البيانات:

قاعدة البيانات المركزية:
- المديرين: {adminCount}
- الصلاحيات: {permissionsCount}

قاعدة بيانات الصيدلية:
- الصيدليات: {pharmacyCount}

الحالة: جميع الجداول متاحة ✓";

                MessageBox.Show(message, "اختبار محتوى قواعد البيانات", 
                               MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار محتوى قواعد البيانات: {ex.Message}", "خطأ", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void TestPharmacyOperations()
        {
            try
            {
                // Test adding a pharmacy
                PharmacyInfo testPharmacy = new PharmacyInfo
                {
                    PharmacyName = "صيدلية الاختبار",
                    OwnerName = "مالك الاختبار",
                    LicenseNumber = "LIC_TEST_001",
                    Address = "عنوان الاختبار",
                    City = "الرياض",
                    Region = "الرياض",
                    Phone = "**********",
                    Email = "<EMAIL>",
                };

                // Check if test pharmacy already exists
                DataTable existingPharmacies = PharmacyManager.SearchPharmacies("TEST001");
                if (existingPharmacies.Rows.Count > 0)
                {
                    MessageBox.Show("صيدلية الاختبار موجودة مسبقاً", "اختبار العمليات", 
                                   MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Try to add the test pharmacy
                bool addResult = PharmacyManager.AddPharmacy(testPharmacy);
                
                if (addResult)
                {
                    MessageBox.Show("تم إضافة صيدلية الاختبار بنجاح ✓", "اختبار العمليات", 
                                   MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Test retrieving pharmacies
                    DataTable allPharmacies = PharmacyManager.GetAllPharmacies();
                    MessageBox.Show($"تم جلب {allPharmacies.Rows.Count} صيدلية من قاعدة البيانات ✓", 
                                   "اختبار العمليات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("فشل في إضافة صيدلية الاختبار ✗", "اختبار العمليات", 
                                   MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار عمليات الصيدلية: {ex.Message}", "خطأ", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void ShowDatabaseInfo()
        {
            try
            {
                // Get admin connection string info
                string adminInfo = "قاعدة البيانات المركزية: PharmacyAdminSystem";
                
                // Get pharmacy connection string info  
                string pharmacyInfo = "قاعدة بيانات الصيدلية: pharmacy";

                // Get table counts
                DataTable adminUsersCount = DatabaseConnection.ExecuteAdminQuery("SELECT COUNT(*) FROM admin_users");
                DataTable permissionsCount = DatabaseConnection.ExecuteAdminQuery("SELECT COUNT(*) FROM admin_permissions");
                DataTable pharmaciesCount = DatabaseConnection.ExecutePharmacyQuery("SELECT COUNT(*) FROM pharmacies");

                string info = $@"
معلومات قواعد البيانات:

{adminInfo}
- المديرين: {adminUsersCount.Rows[0][0]}
- الصلاحيات: {permissionsCount.Rows[0][0]}

{pharmacyInfo}
- الصيدليات: {pharmaciesCount.Rows[0][0]}

سلاسل الاتصال:
- المركزية: Data Source=.;Initial Catalog=PharmacyAdminSystem;...
- الصيدلية: Data Source=.;Initial Catalog=pharmacy;...

الحالة: متصل ✓";

                MessageBox.Show(info, "معلومات قواعد البيانات", 
                               MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض معلومات قواعد البيانات: {ex.Message}", "خطأ",
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void CreateMissingTables()
        {
            try
            {
                // Check and create pharmacies table if missing
                string checkPharmaciesTable = @"
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
                    BEGIN
                        CREATE TABLE pharmacies (
                            id INT IDENTITY(1,1) PRIMARY KEY,
                            pharmacyCode VARCHAR(20) UNIQUE NOT NULL,
                            pharmacyName NVARCHAR(250) NOT NULL,
                            ownerName NVARCHAR(250) NOT NULL,
                            licenseNumber VARCHAR(100) UNIQUE NOT NULL,
                            address NVARCHAR(500) NOT NULL,
                            city NVARCHAR(100) NOT NULL,
                            region NVARCHAR(100) NOT NULL,
                            phone VARCHAR(20) NOT NULL,
                            email VARCHAR(250) NOT NULL,
                            latitude DECIMAL(10, 8) NULL,
                            longitude DECIMAL(11, 8) NULL,
                            isActive BIT DEFAULT 1,
                            registrationDate DATETIME DEFAULT GETDATE(),
                            lastOnline DATETIME DEFAULT GETDATE(),
                            subscriptionType VARCHAR(50) DEFAULT 'Basic',
                            subscriptionExpiry DATETIME NULL,
                            connectionString NVARCHAR(500) NULL,
                            apiKey VARCHAR(100) UNIQUE NULL,
                            createdAt DATETIME DEFAULT GETDATE(),
                            updatedAt DATETIME DEFAULT GETDATE()
                        );
                        SELECT 'تم إنشاء جدول pharmacies' as Result;
                    END
                    ELSE
                    BEGIN
                        SELECT 'جدول pharmacies موجود مسبقاً' as Result;
                    END";

                DataTable result = DatabaseConnection.ExecutePharmacyQuery(checkPharmaciesTable);
                string message = result.Rows[0]["Result"].ToString();

                MessageBox.Show($"تحديث قاعدة البيانات:\n{message}", "تحديث قاعدة البيانات",
                               MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث قاعدة البيانات: {ex.Message}", "خطأ",
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void InsertSampleData()
        {
            try
            {
                // Check if sample data already exists
                DataTable existingData = DatabaseConnection.ExecutePharmacyQuery("SELECT COUNT(*) FROM pharmacies");
                int count = Convert.ToInt32(existingData.Rows[0][0]);

                if (count > 0)
                {
                    MessageBox.Show($"يوجد {count} صيدلية في قاعدة البيانات بالفعل", "بيانات موجودة",
                                   MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Insert sample pharmacies
                string insertSample = @"
                    INSERT INTO pharmacies (pharmacyCode, pharmacyName, ownerName, licenseNumber, address, city, region, phone, email, subscriptionType, apiKey)
                    VALUES
                    ('**********', 'صيدلية النور', 'أحمد محمد علي', 'LIC001', 'شارع الملك فهد، حي النور', 'الرياض', 'الرياض', '**********', '<EMAIL>', 'Premium', NEWID()),
                    ('**********', 'صيدلية الشفاء', 'فاطمة أحمد', 'LIC002', 'طريق الملك عبدالعزيز، حي الملز', 'الرياض', 'الرياض', '**********', '<EMAIL>', 'Basic', NEWID()),
                    ('**********', 'صيدلية الحياة', 'محمد سالم', 'LIC003', 'شارع التحلية، حي السلامة', 'جدة', 'مكة المكرمة', '**********', '<EMAIL>', 'Premium', NEWID());";

                int result = DatabaseConnection.ExecutePharmacyNonQuery(insertSample);

                MessageBox.Show($"تم إدراج {result} صيدلية تجريبية في قاعدة البيانات", "تم إدراج البيانات",
                               MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إدراج البيانات التجريبية: {ex.Message}", "خطأ",
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
