using System;
using System.Data;
using System.Windows.Forms;

namespace admino
{
    public static class QuickTest
    {
        public static void RunDatabaseTest()
        {
            try
            {
                MessageBox.Show("بدء اختبار قاعدة البيانات...", "اختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Test 1: Connection
                bool adminConnected = DatabaseConnection.TestAdminConnection();
                bool pharmacyConnected = DatabaseConnection.TestPharmacyConnection();

                if (!adminConnected || !pharmacyConnected)
                {
                    MessageBox.Show($"فشل الاتصال:\nقاعدة البيانات المركزية: {(adminConnected ? "متصل" : "غير متصل")}\nقاعدة بيانات الصيدلية: {(pharmacyConnected ? "متصل" : "غير متصل")}", 
                                  "خطأ في الاتصال", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Test 2: Check tables exist
                try
                {
                    DataTable adminUsers = DatabaseConnection.ExecuteAdminQuery("SELECT COUNT(*) FROM admin_users");
                    DataTable pharmacies = DatabaseConnection.ExecutePharmacyQuery("SELECT COUNT(*) FROM pharmacies");
                    
                    int adminCount = Convert.ToInt32(adminUsers.Rows[0][0]);
                    int pharmacyCount = Convert.ToInt32(pharmacies.Rows[0][0]);

                    MessageBox.Show($"الجداول موجودة ومتاحة:\n- المديرين: {adminCount}\n- الصيدليات: {pharmacyCount}", 
                                  "نجح الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Test 3: Try to add a test pharmacy
                    if (MessageBox.Show("هل تريد إضافة صيدلية تجريبية؟", "اختبار الإضافة", 
                                       MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        AddTestPharmacy();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في الوصول للجداول: {ex.Message}\n\nتأكد من تشغيل ملف Database_Setup.sql أولاً", 
                                  "خطأ في الجداول", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ عام في الاختبار: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void AddTestPharmacy()
        {
            try
            {
                // Check if test pharmacy already exists
                DataTable existing = DatabaseConnection.ExecutePharmacyQuery(
                    "SELECT COUNT(*) FROM pharmacies WHERE licenseNumber = 'TEST_LIC_001'");
                
                if (Convert.ToInt32(existing.Rows[0][0]) > 0)
                {
                    MessageBox.Show("الصيدلية التجريبية موجودة مسبقاً", "موجود", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                PharmacyInfo testPharmacy = new PharmacyInfo
                {
                    PharmacyName = "Test Pharmacy",
                    PharmacyNameAr = "صيدلية الاختبار",
                    OwnerName = "Test Owner",
                    OwnerNameAr = "مالك الاختبار",
                    LicenseNumber = "TEST_LIC_001",
                    TaxNumber = "TEST_TAX_001",
                    Email = "<EMAIL>",
                    Phone = "**********",
                    Mobile = "**********",
                    Address = "Test Address",
                    AddressAr = "عنوان الاختبار",
                    City = "Riyadh",
                    CityAr = "الرياض",
                    Region = "Riyadh",
                    RegionAr = "الرياض",
                    PostalCode = "12345",
                    Status = "Pending",
                    SubscriptionStatus = "Trial",
                    Notes = "صيدلية تجريبية للاختبار"
                };

                bool success = PharmacyManager.AddPharmacy(testPharmacy);

                if (success)
                {
                    MessageBox.Show("تم إضافة الصيدلية التجريبية بنجاح!\nيمكنك الآن رؤيتها في قائمة الصيدليات", 
                                  "نجح الإضافة", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    // Show updated count
                    DataTable allPharmacies = PharmacyManager.GetAllPharmacies();
                    MessageBox.Show($"إجمالي الصيدليات الآن: {allPharmacies.Rows.Count}", 
                                  "تحديث العدد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("فشل في إضافة الصيدلية التجريبية", "فشل", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الصيدلية التجريبية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void ShowPharmacyData()
        {
            try
            {
                DataTable pharmacies = PharmacyManager.GetAllPharmacies();
                
                if (pharmacies.Rows.Count == 0)
                {
                    MessageBox.Show("لا توجد صيدليات في قاعدة البيانات", "لا توجد بيانات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                string data = "الصيدليات الموجودة في قاعدة البيانات:\n\n";
                
                foreach (DataRow row in pharmacies.Rows)
                {
                    data += $"• {row["pharmacyNameAr"]} - {row["ownerNameAr"]} - {row["cityAr"]} - {row["status"]}\n";
                }

                MessageBox.Show(data, $"قائمة الصيدليات ({pharmacies.Rows.Count})", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض بيانات الصيدليات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void TestLogin()
        {
            try
            {
                // Test login with default credentials
                bool loginResult = AdminUserManager.Login("admin", "Admin@123");
                
                if (loginResult)
                {
                    MessageBox.Show($"تم تسجيل الدخول بنجاح!\nمرحباً {AdminUserManager.CurrentUser.FullNameAr}", 
                                  "نجح تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("فشل في تسجيل الدخول\nتحقق من بيانات المدير الافتراضي في قاعدة البيانات", 
                                  "فشل تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار تسجيل الدخول: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
