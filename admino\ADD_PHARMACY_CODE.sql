-- =============================================
-- إضافة عمود كود الصيدلية إلى الجدول الموجود
-- Add Pharmacy Code Column to Existing Table
-- =============================================

USE UnifiedPharmacy;
GO

PRINT 'بدء إضافة عمود كود الصيدلية...';

-- التحقق من وجود العمود
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'pharmacyCode')
BEGIN
    -- إضافة العمود الجديد
    ALTER TABLE pharmacies 
    ADD pharmacyCode NVARCHAR(20);
    
    PRINT 'تم إضافة عمود pharmacyCode';
    
    -- تحديث البيانات الموجودة بأكواد تلقائية
    UPDATE pharmacies 
    SET pharmacyCode = 'PH2024' + RIGHT('000' + CAST(id as NVARCHAR), 3)
    WHERE pharmacyCode IS NULL;
    
    PRINT 'تم تحديث البيانات الموجودة بأكواد تلقائية';
    
    -- جعل العمود مطلوب وفريد
    ALTER TABLE pharmacies 
    ALTER COLUMN pharmacyCode NVARCHAR(20) NOT NULL;
    
    ALTER TABLE pharmacies 
    ADD CONSTRAINT UK_pharmacies_pharmacyCode UNIQUE (pharmacyCode);
    
    PRINT 'تم تطبيق القيود على العمود';
END
ELSE
BEGIN
    PRINT 'عمود pharmacyCode موجود مسبقاً';
    
    -- تحديث البيانات التي لا تحتوي على كود
    UPDATE pharmacies 
    SET pharmacyCode = 'PH2024' + RIGHT('000' + CAST(id as NVARCHAR), 3)
    WHERE pharmacyCode IS NULL OR pharmacyCode = '';
    
    PRINT 'تم تحديث البيانات الفارغة';
END

-- عرض النتائج
SELECT TOP 10 id, pharmacyCode, pharmacyNameAr, cityAr 
FROM pharmacies 
ORDER BY id;

PRINT 'تم الانتهاء من إضافة عمود كود الصيدلية بنجاح!';
