using System;

namespace admino
{
    public class PharmacyInfo
    {
        public int Id { get; set; }

        // Pharmacy Code (Auto-generated)
        public string PharmacyCode { get; set; }

        // Pharmacy Names
        public string PharmacyName { get; set; }
        public string PharmacyNameAr { get; set; }
        
        // Owner Names
        public string OwnerName { get; set; }
        public string OwnerNameAr { get; set; }
        
        // License and Tax Information
        public string LicenseNumber { get; set; }
        public string TaxNumber { get; set; }
        
        // Contact Information
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Mobile { get; set; }
        
        // Address Information
        public string Address { get; set; }
        public string AddressAr { get; set; }
        public string City { get; set; }
        public string CityAr { get; set; }
        public string Region { get; set; }
        public string RegionAr { get; set; }
        public string PostalCode { get; set; }
        
        // Location Coordinates
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        
        // Status Information
        public string Status { get; set; } = "Pending";
        public bool IsActive { get; set; } = true;
        
        // Subscription Information
        public string SubscriptionStatus { get; set; } = "Trial";
        
        // Dates
        public DateTime RegistrationDate { get; set; } = DateTime.Now;
        public DateTime? ApprovalDate { get; set; }
        public int? ApprovedBy { get; set; }
        public DateTime? LastActivityDate { get; set; }
        
        // Additional Information
        public string Notes { get; set; }
        
        // Constructor
        public PharmacyInfo()
        {
            RegistrationDate = DateTime.Now;
            Status = "Pending";
            SubscriptionStatus = "Trial";
            IsActive = true;
            PharmacyCode = GeneratePharmacyCode();
        }
        
        // Constructor with basic information
        public PharmacyInfo(string pharmacyName, string pharmacyNameAr, string ownerName, string ownerNameAr,
                           string licenseNumber, string email, string phone)
        {
            PharmacyName = pharmacyName;
            PharmacyNameAr = pharmacyNameAr;
            OwnerName = ownerName;
            OwnerNameAr = ownerNameAr;
            LicenseNumber = licenseNumber;
            Email = email;
            Phone = phone;
            RegistrationDate = DateTime.Now;
            Status = "Pending";
            SubscriptionStatus = "Trial";
            IsActive = true;
            PharmacyCode = GeneratePharmacyCode();
        }

        // Generate unique pharmacy code
        private string GeneratePharmacyCode()
        {
            // Format: PH + Year + Month + Random 4 digits
            // Example: PH202412001, PH202412002, etc.
            string year = DateTime.Now.Year.ToString();
            string month = DateTime.Now.Month.ToString("00");
            string random = new Random().Next(1000, 9999).ToString();

            return $"PH{year}{month}{random}";
        }

        // Generate pharmacy code based on city and name
        public static string GeneratePharmacyCodeAdvanced(string cityAr, string pharmacyNameAr)
        {
            try
            {
                // Get first 2 letters from city name (Arabic)
                string cityCode = "";
                if (!string.IsNullOrEmpty(cityAr) && cityAr.Length >= 2)
                {
                    cityCode = cityAr.Substring(0, 2).ToUpper();
                }
                else
                {
                    cityCode = "XX";
                }

                // Get first 2 letters from pharmacy name (Arabic)
                string nameCode = "";
                if (!string.IsNullOrEmpty(pharmacyNameAr) && pharmacyNameAr.Length >= 2)
                {
                    nameCode = pharmacyNameAr.Substring(0, 2).ToUpper();
                }
                else
                {
                    nameCode = "XX";
                }

                // Current year (last 2 digits)
                string year = DateTime.Now.Year.ToString().Substring(2);

                // Random 3 digits
                string random = new Random().Next(100, 999).ToString();

                // Format: CityCode + NameCode + Year + Random
                // Example: الرصيدلية الشفاء -> الرش24001
                return $"{cityCode}{nameCode}{year}{random}";
            }
            catch
            {
                // Fallback to simple format if Arabic processing fails
                string year = DateTime.Now.Year.ToString();
                string month = DateTime.Now.Month.ToString("00");
                string random = new Random().Next(1000, 9999).ToString();
                return $"PH{year}{month}{random}";
            }
        }
        
        // Validation method
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(PharmacyName) &&
                   !string.IsNullOrWhiteSpace(PharmacyNameAr) &&
                   !string.IsNullOrWhiteSpace(OwnerName) &&
                   !string.IsNullOrWhiteSpace(OwnerNameAr) &&
                   !string.IsNullOrWhiteSpace(LicenseNumber) &&
                   !string.IsNullOrWhiteSpace(Email) &&
                   !string.IsNullOrWhiteSpace(Phone);
        }
        
        // Display name for UI
        public string DisplayName => $"{PharmacyNameAr} ({PharmacyName})";
        
        // Owner display name for UI
        public string OwnerDisplayName => $"{OwnerNameAr} ({OwnerName})";
        
        // Full address display
        public string FullAddress => $"{AddressAr}, {CityAr}, {RegionAr}";
        
        // Status display in Arabic
        public string StatusAr
        {
            get
            {
                switch (Status?.ToLower())
                {
                    case "pending": return "في الانتظار";
                    case "approved": return "مُوافق عليه";
                    case "rejected": return "مرفوض";
                    case "suspended": return "معلق";
                    default: return Status ?? "غير محدد";
                }
            }
        }
        
        // Subscription status display in Arabic
        public string SubscriptionStatusAr
        {
            get
            {
                switch (SubscriptionStatus?.ToLower())
                {
                    case "trial": return "تجريبي";
                    case "basic": return "أساسي";
                    case "premium": return "مميز";
                    case "enterprise": return "مؤسسي";
                    default: return SubscriptionStatus ?? "غير محدد";
                }
            }
        }
        
        // Override ToString for debugging
        public override string ToString()
        {
            return $"{PharmacyNameAr} - {OwnerNameAr} - {CityAr}";
        }
    }
}
