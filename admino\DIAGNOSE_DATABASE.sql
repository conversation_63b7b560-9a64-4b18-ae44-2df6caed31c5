-- =============================================
-- تشخيص قاعدة البيانات UnifiedPharmacy
-- Diagnose UnifiedPharmacy Database
-- =============================================

USE UnifiedPharmacy;
GO

PRINT '=== تشخيص قاعدة البيانات UnifiedPharmacy ===';

-- 1. التحقق من وجود الجدول
IF EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    PRINT '✅ جدول pharmacies موجود';
    
    -- 2. عرض جميع الأعمدة الموجودة
    PRINT '';
    PRINT '📋 الأعمدة الموجودة في الجدول:';
    SELECT 
        ROW_NUMBER() OVER (ORDER BY ORDINAL_POSITION) as [#],
        COLUMN_NAME as [اسم العمود],
        DATA_TYPE as [نوع البيانات],
        IS_NULLABLE as [يقبل NULL],
        COLUMN_DEFAULT as [القيمة الافتراضية]
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'pharmacies'
    ORDER BY ORDINAL_POSITION;
    
    -- 3. التحقق من الأعمدة المطلوبة
    PRINT '';
    PRINT '🔍 التحقق من الأعمدة المطلوبة:';
    
    DECLARE @requiredColumns TABLE (ColumnName NVARCHAR(50));
    INSERT INTO @requiredColumns VALUES 
    ('id'), ('pharmacyName'), ('pharmacyNameAr'), ('ownerName'), ('ownerNameAr'),
    ('licenseNumber'), ('taxNumber'), ('email'), ('phone'), ('mobile'),
    ('address'), ('addressAr'), ('city'), ('cityAr'), ('region'), ('regionAr'),
    ('postalCode'), ('latitude'), ('longitude'), ('status'), ('isActive'),
    ('registrationDate'), ('approvalDate'), ('approvedBy'), ('lastActivityDate'),
    ('subscriptionStatus'), ('subscriptionStartDate'), ('subscriptionEndDate'), ('notes');
    
    SELECT 
        r.ColumnName as [العمود المطلوب],
        CASE 
            WHEN c.COLUMN_NAME IS NOT NULL THEN '✅ موجود'
            ELSE '❌ مفقود'
        END as [الحالة]
    FROM @requiredColumns r
    LEFT JOIN INFORMATION_SCHEMA.COLUMNS c 
        ON r.ColumnName = c.COLUMN_NAME 
        AND c.TABLE_NAME = 'pharmacies'
    ORDER BY r.ColumnName;
    
    -- 4. عدد السجلات
    DECLARE @recordCount INT;
    SELECT @recordCount = COUNT(*) FROM pharmacies;
    PRINT '';
    PRINT '📊 عدد السجلات في الجدول: ' + CAST(@recordCount AS NVARCHAR);
    
    -- 5. عرض عينة من البيانات
    IF @recordCount > 0
    BEGIN
        PRINT '';
        PRINT '📄 عينة من البيانات (أول 3 سجلات):';
        SELECT TOP 3 
            id,
            pharmacyName,
            pharmacyNameAr,
            ownerName,
            licenseNumber,
            email,
            city,
            status
        FROM pharmacies
        ORDER BY id;
    END
    
    -- 6. اختبار استعلام بسيط
    BEGIN TRY
        DECLARE @testCount INT;
        SELECT @testCount = COUNT(*) FROM pharmacies WHERE status = 'Approved';
        PRINT '';
        PRINT '✅ اختبار الاستعلام نجح - عدد الصيدليات المعتمدة: ' + CAST(@testCount AS NVARCHAR);
    END TRY
    BEGIN CATCH
        PRINT '';
        PRINT '❌ فشل اختبار الاستعلام: ' + ERROR_MESSAGE();
    END CATCH
    
END
ELSE
BEGIN
    PRINT '❌ جدول pharmacies غير موجود!';
    PRINT '';
    PRINT '💡 الحل: نفذ الملف COMPLETE_FIX_UnifiedPharmacy.sql لإنشاء الجدول';
END

-- 7. التحقق من قاعدة البيانات نفسها
PRINT '';
PRINT '🗄️ معلومات قاعدة البيانات:';
SELECT 
    name as [اسم قاعدة البيانات],
    collation_name as [الترميز],
    create_date as [تاريخ الإنشاء],
    state_desc as [الحالة]
FROM sys.databases 
WHERE name = 'UnifiedPharmacy';

PRINT '';
PRINT '=== انتهى التشخيص ===';

-- 8. توصيات الإصلاح
PRINT '';
PRINT '💡 توصيات الإصلاح:';
PRINT '1. إذا كان الجدول مفقود أو ناقص، نفذ: COMPLETE_FIX_UnifiedPharmacy.sql';
PRINT '2. إذا كانت الأعمدة مفقودة، نفذ: Fix_UnifiedPharmacy_Columns.sql';
PRINT '3. تأكد من صلاحيات الوصول لقاعدة البيانات';
PRINT '4. تأكد من تشغيل SQL Server بشكل صحيح';
