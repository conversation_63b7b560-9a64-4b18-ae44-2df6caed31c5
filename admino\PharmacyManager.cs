using System;
using System.Data;
using System.Data.SqlClient;

namespace admino
{
    public class PharmacyManager
    {
        // Add new pharmacy
        public static bool AddPharmacy(PharmacyInfo pharmacy)
        {
            try
            {
                // Check if license number already exists
                if (LicenseNumberExists(pharmacy.LicenseNumber))
                {
                    throw new Exception("رقم الترخيص موجود مسبقاً");
                }

                // Check if email already exists (only if email is provided)
                if (!string.IsNullOrWhiteSpace(pharmacy.Email) && EmailExists(pharmacy.Email))
                {
                    throw new Exception("البريد الإلكتروني موجود مسبقاً");
                }

                string query = @"
                    INSERT INTO pharmacies
                    (pharmacyCode, pharmacyName, pharmacyNameAr, ownerName, ownerNameAr, licenseNumber, taxNumber,
                     email, phone, mobile, address, addressAr, city, cityAr, region, regionAr,
                     postalCode, latitude, longitude, status, subscriptionStatus, notes)
                    VALUES
                    (@pharmacyCode, @pharmacyName, @pharmacyNameAr, @ownerName, @ownerNameAr, @licenseNumber, @taxNumber,
                     @email, @phone, @mobile, @address, @addressAr, @city, @cityAr, @region, @regionAr,
                     @postalCode, @latitude, @longitude, @status, @subscriptionStatus, @notes)";

                SqlParameter[] parameters = {
                    new SqlParameter("@pharmacyCode", pharmacy.PharmacyCode ?? ""),
                    new SqlParameter("@pharmacyName", pharmacy.PharmacyName ?? ""),
                    new SqlParameter("@pharmacyNameAr", pharmacy.PharmacyNameAr ?? ""),
                    new SqlParameter("@ownerName", pharmacy.OwnerName ?? ""),
                    new SqlParameter("@ownerNameAr", pharmacy.OwnerNameAr ?? ""),
                    new SqlParameter("@licenseNumber", pharmacy.LicenseNumber ?? ""),
                    new SqlParameter("@taxNumber", pharmacy.TaxNumber ?? ""),
                    new SqlParameter("@email", pharmacy.Email ?? ""),
                    new SqlParameter("@phone", pharmacy.Phone ?? ""),
                    new SqlParameter("@mobile", pharmacy.Mobile ?? ""),
                    new SqlParameter("@address", pharmacy.Address ?? ""),
                    new SqlParameter("@addressAr", pharmacy.AddressAr ?? ""),
                    new SqlParameter("@city", pharmacy.City ?? ""),
                    new SqlParameter("@cityAr", pharmacy.CityAr ?? ""),
                    new SqlParameter("@region", pharmacy.Region ?? ""),
                    new SqlParameter("@regionAr", pharmacy.RegionAr ?? ""),
                    new SqlParameter("@postalCode", pharmacy.PostalCode ?? ""),
                    new SqlParameter("@latitude", (object)pharmacy.Latitude ?? DBNull.Value),
                    new SqlParameter("@longitude", (object)pharmacy.Longitude ?? DBNull.Value),
                    new SqlParameter("@status", pharmacy.Status ?? "Pending"),
                    new SqlParameter("@subscriptionStatus", pharmacy.SubscriptionStatus ?? "Trial"),
                    new SqlParameter("@notes", pharmacy.Notes ?? "")
                };

                int result = DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);

                if (result > 0)
                {
                    // Log activity
                    AdminUserManager.LogActivity(
                        AdminUserManager.CurrentUser.Id,
                        "ADD_PHARMACY",
                        "إضافة صيدلية",
                        "Pharmacy",
                        null,
                        pharmacy.PharmacyNameAr,
                        $"Added new pharmacy: {pharmacy.PharmacyName}",
                        $"تم إضافة صيدلية جديدة: {pharmacy.PharmacyNameAr}"
                    );

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                // Log the detailed error for debugging
                System.Diagnostics.Debug.WriteLine($"AddPharmacy Error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");

                // Provide user-friendly error messages
                if (ex.Message.Contains("Cannot open database") || ex.Message.Contains("server was not found"))
                {
                    throw new Exception("خطأ في الاتصال بقاعدة البيانات. تحقق من تشغيل SQL Server ووجود قاعدة البيانات 'UnifiedPharmacy'.");
                }
                else if (ex.Message.Contains("Invalid object name 'pharmacies'"))
                {
                    throw new Exception("جدول الصيدليات غير موجود. يرجى تنفيذ ملف Database_Setup.sql أولاً.");
                }
                else if (ex.Message.Contains("UNIQUE KEY constraint"))
                {
                    if (ex.Message.Contains("licenseNumber"))
                        throw new Exception("رقم الترخيص موجود مسبقاً");
                    else if (ex.Message.Contains("email"))
                        throw new Exception("البريد الإلكتروني موجود مسبقاً");
                    else
                        throw new Exception("البيانات المدخلة موجودة مسبقاً");
                }
                else
                {
                    throw new Exception($"خطأ في إضافة الصيدلية: {ex.Message}");
                }
            }
        }

        // Update pharmacy information
        public static bool UpdatePharmacy(PharmacyInfo pharmacy)
        {
            try
            {
                string query = @"
                    UPDATE pharmacies
                    SET pharmacyName = @pharmacyName,
                        pharmacyNameAr = @pharmacyNameAr,
                        ownerName = @ownerName,
                        ownerNameAr = @ownerNameAr,
                        licenseNumber = @licenseNumber,
                        taxNumber = @taxNumber,
                        email = @email,
                        phone = @phone,
                        mobile = @mobile,
                        address = @address,
                        addressAr = @addressAr,
                        city = @city,
                        cityAr = @cityAr,
                        region = @region,
                        regionAr = @regionAr,
                        postalCode = @postalCode,
                        latitude = @latitude,
                        longitude = @longitude,
                        status = @status,
                        subscriptionStatus = @subscriptionStatus,
                        notes = @notes,
                        lastActivityDate = GETDATE()
                    WHERE id = @id";

                SqlParameter[] parameters = {
                    new SqlParameter("@id", pharmacy.Id),
                    new SqlParameter("@pharmacyName", pharmacy.PharmacyName),
                    new SqlParameter("@pharmacyNameAr", pharmacy.PharmacyNameAr),
                    new SqlParameter("@ownerName", pharmacy.OwnerName),
                    new SqlParameter("@ownerNameAr", pharmacy.OwnerNameAr),
                    new SqlParameter("@licenseNumber", pharmacy.LicenseNumber),
                    new SqlParameter("@taxNumber", pharmacy.TaxNumber ?? ""),
                    new SqlParameter("@email", pharmacy.Email),
                    new SqlParameter("@phone", pharmacy.Phone),
                    new SqlParameter("@mobile", pharmacy.Mobile ?? ""),
                    new SqlParameter("@address", pharmacy.Address),
                    new SqlParameter("@addressAr", pharmacy.AddressAr),
                    new SqlParameter("@city", pharmacy.City),
                    new SqlParameter("@cityAr", pharmacy.CityAr),
                    new SqlParameter("@region", pharmacy.Region ?? ""),
                    new SqlParameter("@regionAr", pharmacy.RegionAr ?? ""),
                    new SqlParameter("@postalCode", pharmacy.PostalCode ?? ""),
                    new SqlParameter("@latitude", (object)pharmacy.Latitude ?? DBNull.Value),
                    new SqlParameter("@longitude", (object)pharmacy.Longitude ?? DBNull.Value),
                    new SqlParameter("@status", pharmacy.Status ?? "Pending"),
                    new SqlParameter("@subscriptionStatus", pharmacy.SubscriptionStatus ?? "Trial"),
                    new SqlParameter("@notes", pharmacy.Notes ?? "")
                };

                int result = DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);

                if (result > 0)
                {
                    // Log activity
                    AdminUserManager.LogActivity(
                        AdminUserManager.CurrentUser.Id,
                        "UPDATE_PHARMACY",
                        "تحديث صيدلية",
                        "Pharmacy",
                        pharmacy.Id,
                        pharmacy.PharmacyNameAr,
                        $"Updated pharmacy: {pharmacy.PharmacyName}",
                        $"تم تحديث الصيدلية: {pharmacy.PharmacyNameAr}"
                    );

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                // Log the detailed error for debugging
                System.Diagnostics.Debug.WriteLine($"UpdatePharmacy Error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");

                // Provide user-friendly error messages
                if (ex.Message.Contains("Cannot open database") || ex.Message.Contains("server was not found"))
                {
                    throw new Exception("خطأ في الاتصال بقاعدة البيانات. تحقق من تشغيل SQL Server ووجود قاعدة البيانات 'UnifiedPharmacy'.");
                }
                else if (ex.Message.Contains("Invalid object name 'pharmacies'"))
                {
                    throw new Exception("جدول الصيدليات غير موجود. يرجى تنفيذ ملف Database_Setup.sql أولاً.");
                }
                else if (ex.Message.Contains("UNIQUE KEY constraint"))
                {
                    if (ex.Message.Contains("licenseNumber"))
                        throw new Exception("رقم الترخيص موجود مسبقاً");
                    else if (ex.Message.Contains("email"))
                        throw new Exception("البريد الإلكتروني موجود مسبقاً");
                    else
                        throw new Exception("البيانات المدخلة موجودة مسبقاً");
                }
                else
                {
                    throw new Exception($"خطأ في تحديث الصيدلية: {ex.Message}");
                }
            }
        }

        // Get all pharmacies
        public static DataTable GetAllPharmacies()
        {
            try
            {
                // Try with all columns first
                string query = @"
                    SELECT id,
                           ISNULL(pharmacyCode, '') as pharmacyCode,
                           pharmacyName, pharmacyNameAr, ownerName, ownerNameAr,
                           licenseNumber,
                           ISNULL(taxNumber, '') as taxNumber,
                           email, phone,
                           ISNULL(mobile, '') as mobile,
                           address, addressAr, city, cityAr,
                           ISNULL(region, '') as region,
                           ISNULL(regionAr, '') as regionAr,
                           ISNULL(postalCode, '') as postalCode,
                           latitude, longitude,
                           ISNULL(status, 'Pending') as status,
                           ISNULL(CAST(isActive as BIT), 1) as isActive,
                           ISNULL(registrationDate, GETDATE()) as registrationDate,
                           approvalDate, lastActivityDate,
                           ISNULL(subscriptionStatus, 'Trial') as subscriptionStatus,
                           ISNULL(notes, '') as notes
                    FROM pharmacies
                    ORDER BY pharmacyNameAr";

                return DatabaseConnection.ExecutePharmacyQuery(query);
            }
            catch (Exception ex)
            {
                // If the above fails, try with basic columns only
                try
                {
                    string basicQuery = @"
                        SELECT id, pharmacyName, pharmacyNameAr, ownerName, ownerNameAr,
                               licenseNumber, email, phone, address, addressAr,
                               city, cityAr, latitude, longitude
                        FROM pharmacies
                        ORDER BY pharmacyNameAr";

                    return DatabaseConnection.ExecutePharmacyQuery(basicQuery);
                }
                catch (Exception basicEx)
                {
                    throw new Exception($"خطأ في جلب بيانات الصيدليات: {ex.Message}\n\nيرجى تنفيذ ملف Fix_Database_Columns.sql لإصلاح بنية الجدول.");
                }
            }
        }

        // Get pharmacy by ID
        public static PharmacyInfo GetPharmacyById(int id)
        {
            try
            {
                string query = @"
                    SELECT * FROM pharmacies WHERE id = @id";

                SqlParameter[] parameters = {
                    new SqlParameter("@id", id)
                };

                DataTable result = DatabaseConnection.ExecutePharmacyQuery(query, parameters);

                if (result.Rows.Count > 0)
                {
                    DataRow row = result.Rows[0];
                    return new PharmacyInfo
                    {
                        Id = Convert.ToInt32(row["id"]),
                        PharmacyName = row["pharmacyName"].ToString(),
                        PharmacyNameAr = row["pharmacyNameAr"].ToString(),
                        OwnerName = row["ownerName"].ToString(),
                        OwnerNameAr = row["ownerNameAr"].ToString(),
                        LicenseNumber = row["licenseNumber"].ToString(),
                        TaxNumber = row["taxNumber"].ToString(),
                        Email = row["email"].ToString(),
                        Phone = row["phone"].ToString(),
                        Mobile = row["mobile"].ToString(),
                        Address = row["address"].ToString(),
                        AddressAr = row["addressAr"].ToString(),
                        City = row["city"].ToString(),
                        CityAr = row["cityAr"].ToString(),
                        Region = row["region"].ToString(),
                        RegionAr = row["regionAr"].ToString(),
                        PostalCode = row["postalCode"].ToString(),
                        Latitude = row["latitude"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(row["latitude"]),
                        Longitude = row["longitude"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(row["longitude"]),
                        Status = row["status"].ToString(),
                        IsActive = Convert.ToBoolean(row["isActive"]),
                        RegistrationDate = Convert.ToDateTime(row["registrationDate"]),
                        ApprovalDate = row["approvalDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(row["approvalDate"]),
                        ApprovedBy = row["approvedBy"] == DBNull.Value ? (int?)null : Convert.ToInt32(row["approvedBy"]),
                        LastActivityDate = row["lastActivityDate"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(row["lastActivityDate"]),
                        SubscriptionStatus = row["subscriptionStatus"].ToString(),
                        Notes = row["notes"].ToString()
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب بيانات الصيدلية: {ex.Message}");
            }
        }

        // Activate/Deactivate pharmacy
        public static bool SetPharmacyStatus(int pharmacyId, bool isActive)
        {
            try
            {
                string query = @"
                    UPDATE pharmacies 
                    SET isActive = @isActive, updatedAt = GETDATE()
                    WHERE id = @id";

                SqlParameter[] parameters = {
                    new SqlParameter("@id", pharmacyId),
                    new SqlParameter("@isActive", isActive)
                };

                int result = DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);

                if (result > 0)
                {
                    // Get pharmacy name for logging
                    PharmacyInfo pharmacy = GetPharmacyById(pharmacyId);
                    string action = isActive ? "ACTIVATE_PHARMACY" : "DEACTIVATE_PHARMACY";
                    string actionAr = isActive ? "تفعيل صيدلية" : "إلغاء تفعيل صيدلية";
                    string details = isActive ? $"Activated pharmacy: {pharmacy?.PharmacyName}" : $"Deactivated pharmacy: {pharmacy?.PharmacyName}";
                    string detailsAr = isActive ? $"تم تفعيل الصيدلية: {pharmacy?.PharmacyName}" : $"تم إلغاء تفعيل الصيدلية: {pharmacy?.PharmacyName}";

                    AdminUserManager.LogActivity(
                        AdminUserManager.CurrentUser.Id,
                        action,
                        actionAr,
                        "Pharmacy",
                        pharmacyId,
                        pharmacy?.PharmacyName,
                        details,
                        detailsAr
                    );

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تغيير حالة الصيدلية: {ex.Message}");
            }
        }

        // Check if license number exists
        private static bool LicenseNumberExists(string licenseNumber)
        {
            string query = "SELECT COUNT(*) FROM pharmacies WHERE licenseNumber = @licenseNumber";
            SqlParameter[] parameters = {
                new SqlParameter("@licenseNumber", licenseNumber)
            };

            DataTable result = DatabaseConnection.ExecutePharmacyQuery(query, parameters);
            return Convert.ToInt32(result.Rows[0][0]) > 0;
        }

        // Check if email exists
        private static bool EmailExists(string email)
        {
            string query = "SELECT COUNT(*) FROM pharmacies WHERE email = @email";
            SqlParameter[] parameters = {
                new SqlParameter("@email", email)
            };

            DataTable result = DatabaseConnection.ExecutePharmacyQuery(query, parameters);
            return Convert.ToInt32(result.Rows[0][0]) > 0;
        }

        // Search pharmacies
        public static DataTable SearchPharmacies(string searchTerm)
        {
            try
            {
                string query = @"
                    SELECT id, pharmacyName, pharmacyNameAr, ownerName, ownerNameAr,
                           licenseNumber, taxNumber, email, phone, mobile,
                           address, addressAr, city, cityAr, region, regionAr,
                           postalCode, latitude, longitude, status, isActive,
                           registrationDate, approvalDate, lastActivityDate,
                           subscriptionStatus, notes
                    FROM pharmacies
                    WHERE pharmacyName LIKE @searchTerm
                       OR pharmacyNameAr LIKE @searchTerm
                       OR ownerName LIKE @searchTerm
                       OR ownerNameAr LIKE @searchTerm
                       OR licenseNumber LIKE @searchTerm
                       OR city LIKE @searchTerm
                       OR cityAr LIKE @searchTerm
                       OR email LIKE @searchTerm
                       OR phone LIKE @searchTerm
                    ORDER BY pharmacyNameAr";

                SqlParameter[] parameters = {
                    new SqlParameter("@searchTerm", $"%{searchTerm}%")
                };

                return DatabaseConnection.ExecutePharmacyQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن الصيدليات: {ex.Message}");
            }
        }
    }
}
