-- =============================================
-- إعداد نظام إدارة الصيدليات الشامل
-- Complete Pharmacy Management System Setup
-- =============================================

PRINT '🚀 بدء إعداد نظام إدارة الصيدليات الشامل...';
PRINT '';

-- =============================================
-- الخطوة 1: إنشاء قاعدة البيانات UnifiedPharmacy
-- =============================================
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'UnifiedPharmacy')
BEGIN
    CREATE DATABASE UnifiedPharmacy COLLATE Arabic_CI_AS;
    PRINT '✅ تم إنشاء قاعدة البيانات UnifiedPharmacy';
END
ELSE
BEGIN
    PRINT '⚠️ قاعدة البيانات UnifiedPharmacy موجودة مسبقاً';
END
GO

USE UnifiedPharmacy;
GO

-- =============================================
-- الخطوة 2: إنشاء جدول الصيدليات
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyCode NVARCHAR(20) UNIQUE NOT NULL,
        pharmacyName NVARCHAR(100) NOT NULL,
        pharmacyNameAr NVARCHAR(100) NOT NULL,
        ownerName NVARCHAR(100) NOT NULL,
        ownerNameAr NVARCHAR(100) NOT NULL,
        licenseNumber NVARCHAR(50) UNIQUE NOT NULL,
        taxNumber NVARCHAR(50),
        email NVARCHAR(100) UNIQUE NOT NULL,
        phone NVARCHAR(20) NOT NULL,
        mobile NVARCHAR(20),
        address NVARCHAR(200) NOT NULL,
        addressAr NVARCHAR(200) NOT NULL,
        city NVARCHAR(50) NOT NULL,
        cityAr NVARCHAR(50) NOT NULL,
        region NVARCHAR(50),
        regionAr NVARCHAR(50),
        postalCode NVARCHAR(10),
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        status NVARCHAR(20) NOT NULL DEFAULT 'Pending',
        isActive BIT NOT NULL DEFAULT 1,
        registrationDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        approvalDate DATETIME2,
        approvedBy INT,
        lastActivityDate DATETIME2,
        subscriptionStatus NVARCHAR(20) NOT NULL DEFAULT 'Trial',
        subscriptionStartDate DATETIME2 DEFAULT GETDATE(),
        subscriptionEndDate DATETIME2,
        notes NVARCHAR(300)
    );
    PRINT '✅ تم إنشاء جدول pharmacies';
END
ELSE
BEGIN
    PRINT '⚠️ جدول pharmacies موجود مسبقاً';
END

-- =============================================
-- الخطوة 3: إنشاء جداول نظام إدارة المستخدمين
-- =============================================

-- جدول أدوار المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_roles' AND xtype='U')
BEGIN
    CREATE TABLE user_roles (
        id INT IDENTITY(1,1) PRIMARY KEY,
        roleName NVARCHAR(30) UNIQUE NOT NULL,
        roleNameAr NVARCHAR(30) NOT NULL,
        description NVARCHAR(200),
        descriptionAr NVARCHAR(200),
        permissions NVARCHAR(MAX),
        isActive BIT NOT NULL DEFAULT 1,
        createdDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        createdBy INT
    );
    PRINT '✅ تم إنشاء جدول user_roles';
END

-- جدول مستخدمي الصيدليات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacy_users' AND xtype='U')
BEGIN
    CREATE TABLE pharmacy_users (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        username NVARCHAR(50) UNIQUE NOT NULL,
        password NVARCHAR(255) NOT NULL,
        fullName NVARCHAR(100) NOT NULL,
        fullNameAr NVARCHAR(100) NOT NULL,
        email NVARCHAR(100) UNIQUE NOT NULL,
        phone NVARCHAR(20),
        mobile NVARCHAR(20),
        roleId INT NOT NULL,
        isActive BIT NOT NULL DEFAULT 1,
        lastLogin DATETIME2,
        loginAttempts INT NOT NULL DEFAULT 0,
        lockedUntil DATETIME2,
        createdDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        createdBy INT,
        modifiedDate DATETIME2,
        modifiedBy INT,
        profileImage NVARCHAR(255),
        notes NVARCHAR(500),
        sessionToken NVARCHAR(255),
        sessionExpiry DATETIME2,
        
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id) ON DELETE CASCADE,
        FOREIGN KEY (roleId) REFERENCES user_roles(id),
        FOREIGN KEY (createdBy) REFERENCES pharmacy_users(id),
        FOREIGN KEY (modifiedBy) REFERENCES pharmacy_users(id)
    );
    PRINT '✅ تم إنشاء جدول pharmacy_users';
END

-- جدول صلاحيات المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_permissions' AND xtype='U')
BEGIN
    CREATE TABLE user_permissions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        permissionName NVARCHAR(50) UNIQUE NOT NULL,
        permissionNameAr NVARCHAR(50) NOT NULL,
        description NVARCHAR(200),
        descriptionAr NVARCHAR(200),
        category NVARCHAR(30) NOT NULL,
        isActive BIT NOT NULL DEFAULT 1,
        createdDate DATETIME2 NOT NULL DEFAULT GETDATE()
    );
    PRINT '✅ تم إنشاء جدول user_permissions';
END

-- جدول ربط المستخدمين بالصلاحيات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_permission_mapping' AND xtype='U')
BEGIN
    CREATE TABLE user_permission_mapping (
        id INT IDENTITY(1,1) PRIMARY KEY,
        userId INT NOT NULL,
        permissionId INT NOT NULL,
        isActive BIT NOT NULL DEFAULT 1,
        grantedBy INT,
        grantedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        expiryDate DATETIME2,
        
        FOREIGN KEY (userId) REFERENCES pharmacy_users(id) ON DELETE CASCADE,
        FOREIGN KEY (permissionId) REFERENCES user_permissions(id),
        FOREIGN KEY (grantedBy) REFERENCES pharmacy_users(id),
        
        UNIQUE(userId, permissionId)
    );
    PRINT '✅ تم إنشاء جدول user_permission_mapping';
END

-- جدول جلسات المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_sessions' AND xtype='U')
BEGIN
    CREATE TABLE user_sessions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        userId INT NOT NULL,
        sessionToken NVARCHAR(255) UNIQUE NOT NULL,
        ipAddress NVARCHAR(45),
        userAgent NVARCHAR(500),
        loginTime DATETIME2 NOT NULL DEFAULT GETDATE(),
        lastActivity DATETIME2 NOT NULL DEFAULT GETDATE(),
        logoutTime DATETIME2,
        isActive BIT NOT NULL DEFAULT 1,
        
        FOREIGN KEY (userId) REFERENCES pharmacy_users(id) ON DELETE CASCADE
    );
    PRINT '✅ تم إنشاء جدول user_sessions';
END

-- جدول سجل الأنشطة
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_activity_log' AND xtype='U')
BEGIN
    CREATE TABLE user_activity_log (
        id INT IDENTITY(1,1) PRIMARY KEY,
        userId INT,
        pharmacyId INT,
        action NVARCHAR(50) NOT NULL,
        actionAr NVARCHAR(50) NOT NULL,
        tableName NVARCHAR(50),
        recordId INT,
        oldValues NVARCHAR(MAX),
        newValues NVARCHAR(MAX),
        description NVARCHAR(500),
        descriptionAr NVARCHAR(500),
        ipAddress NVARCHAR(45),
        userAgent NVARCHAR(500),
        timestamp DATETIME2 NOT NULL DEFAULT GETDATE(),
        
        FOREIGN KEY (userId) REFERENCES pharmacy_users(id),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول user_activity_log';
END

PRINT '';
PRINT '🎉 تم إنشاء جميع الجداول بنجاح!';
PRINT '';
PRINT '📋 الجداول المنشأة:';
PRINT '   1. pharmacies - جدول الصيدليات';
PRINT '   2. user_roles - جدول أدوار المستخدمين';
PRINT '   3. pharmacy_users - جدول مستخدمي الصيدليات';
PRINT '   4. user_permissions - جدول صلاحيات المستخدمين';
PRINT '   5. user_permission_mapping - جدول ربط المستخدمين بالصلاحيات';
PRINT '   6. user_sessions - جدول جلسات المستخدمين';
PRINT '   7. user_activity_log - جدول سجل الأنشطة';
PRINT '';
PRINT '⏭️ الخطوة التالية: تنفيذ ملف INSERT_PHARMACY_USERS_DATA.sql لإدراج البيانات الأساسية';
