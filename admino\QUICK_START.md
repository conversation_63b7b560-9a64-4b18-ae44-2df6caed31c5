# دليل البدء السريع - نظام إدارة الصيدليات (محدث)

## خطوات التشغيل السريع

### 1. إعداد قاعدة البيانات
```sql
-- افتح SQL Server Management Studio أو أي أداة SQL
-- نفذ الملف: Database_Setup.sql
-- سيتم إنشاء قاعدتي البيانات تلقائياً مع البيانات الأساسية
-- ستحصل على 3 صيدليات تجريبية جاهزة للاختبار
```

### 2. تشغيل التطبيق
1. افتح المشروع في Visual Studio
2. تأكد من تثبيت Guna.UI2.WinForms
3. اضغط F5 لتشغيل التطبيق

### 3. تسجيل الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** Admin@123

### 4. اختبار النظام
بعد تسجيل الدخول، اضغط على "اختبار قاعدة البيانات" ثم:
- "اختبار سريع شامل" - للتحقق من كل شيء
- "عرض بيانات الصيدليات" - لرؤية البيانات الموجودة

## الميزات المتاحة

### ✅ تم تطويرها وتحديثها
- [x] نظام تسجيل الدخول الآمن مع قاعدة البيانات الحقيقية
- [x] إضافة صيدليات جديدة بالهيكل المحدث (دعم العربية والإنجليزية)
- [x] عرض قائمة الصيدليات من قاعدة البيانات الفعلية
- [x] تعديل بيانات الصيدليات مع جميع الحقول الجديدة
- [x] البحث المتقدم في الصيدليات
- [x] نظام الصلاحيات المتكامل
- [x] تسجيل جميع النشاطات في قاعدة البيانات
- [x] إحصائيات الصيدليات الحقيقية
- [x] اختبارات شاملة للتحقق من عمل النظام

### 🆕 الحقول الجديدة المضافة
- أسماء الصيدليات والمالكين بالعربية والإنجليزية
- الرقم الضريبي
- رقم الجوال (إضافي للهاتف)
- العناوين بالعربية والإنجليزية
- المدن والمناطق بالعربية والإنجليزية
- الرمز البريدي
- حالة الطلب (Pending, Approved, Rejected, Suspended)
- حالة الاشتراك (Trial, Basic, Premium, Enterprise)
- ملاحظات
- تواريخ الموافقة وآخر نشاط

### 🔄 قيد التطوير
- [ ] لوحة التحكم الرئيسية
- [ ] التقارير المفصلة
- [ ] نظام الإشعارات
- [ ] النسخ الاحتياطية التلقائية

## الملفات المهمة

### قواعد البيانات
- `Database_Setup.sql` - إعداد قواعد البيانات
- `PharmacyAdminSystem` - قاعدة البيانات المركزية
- `pharmacy` - قاعدة بيانات الصيدليات

### الكود الأساسي
- `DatabaseConnection.cs` - الاتصال بقواعد البيانات
- `AdminUserManager.cs` - إدارة المستخدمين
- `PharmacyManager.cs` - إدارة الصيدليات
- `LoginForm.cs` - نموذج تسجيل الدخول

### الواجهات
- `TestForm.cs` - النموذج الرئيسي
- `add_pharmacy.cs` - إضافة/تعديل الصيدليات
- `uc_pharmacylist.cs` - قائمة الصيدليات

## استكشاف الأخطاء

### مشكلة الاتصال بقاعدة البيانات
```
خطأ: Cannot connect to database
الحل: تحقق من:
1. تشغيل SQL Server
2. صحة اسم الخادم في connection string
3. صلاحيات الوصول لقاعدة البيانات
```

### مشكلة تسجيل الدخول
```
خطأ: Invalid username or password
الحل: استخدم البيانات الافتراضية:
- Username: admin
- Password: Admin@123
```

### مشكلة Guna.UI2
```
خطأ: Could not load Guna.UI2
الحل: 
1. افتح Package Manager Console
2. نفذ: Install-Package Guna.UI2.WinForms
```

## نصائح الاستخدام

## البيانات التجريبية المتاحة

بعد تشغيل `Database_Setup.sql` ستحصل على:

### صيدليات تجريبية:
1. **صيدلية النور** - الرياض (Approved, Premium)
2. **صيدلية الشفاء** - الرياض (Approved, Basic)
3. **صيدلية الحياة** - جدة (Pending, Trial)

### مدير النظام:
- اسم المستخدم: admin
- كلمة المرور: Admin@123
- الدور: SuperAdmin (جميع الصلاحيات)

## كيفية الاستخدام

### إضافة صيدلية جديدة
1. اضغط "إضافة صيدلية"
2. املأ البيانات المطلوبة:
   - **إجباري:** اسم الصيدلية (عربي/إنجليزي)
   - **إجباري:** اسم المالك (عربي/إنجليزي)
   - **إجباري:** رقم الترخيص
   - **إجباري:** البريد الإلكتروني
   - **إجباري:** الهاتف
   - **إجباري:** العنوان (عربي/إنجليزي)
   - **إجباري:** المدينة (عربي/إنجليزي)
   - **اختياري:** باقي الحقول
3. اضغط "حفظ الصيدلية"
4. ستظهر رسالة تأكيد مع عدد الصيدليات الإجمالي

### البحث في الصيدليات
- يمكن البحث بالاسم، المالك، الرمز، أو المدينة
- اضغط Enter في مربع البحث أو اضغط "بحث"
- اضغط "تحديث" لإعادة تحميل جميع الصيدليات

### تعديل صيدلية
- في قائمة الصيدليات، اضغط "تعديل" في الصف المطلوب
- أو اضغط مرتين على الصف لعرض التفاصيل

## الدعم
للحصول على المساعدة:
1. راجع ملف README.md للتفاصيل الكاملة
2. تحقق من سجلات الأخطاء في قاعدة البيانات
3. تواصل مع فريق التطوير

---
**نظام إدارة الصيدليات v1.0**
