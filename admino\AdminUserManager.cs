using System;
using System.Data;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;

namespace admino
{
    public class AdminUserManager
    {
        // Current logged in user
        public static AdminUser CurrentUser { get; set; }

        // Login method
        public static bool Login(string username, string password, string ipAddress = "", string userAgent = "")
        {
            try
            {
                // Check if user exists and is active
                string query = @"
                    SELECT id, username, password, fullName, fullNameAr, email, role, isActive, 
                           loginAttempts, lockedUntil, lastLogin
                    FROM admin_users 
                    WHERE username = @username AND isActive = 1";

                SqlParameter[] parameters = {
                    new SqlParameter("@username", username)
                };

                DataTable result = DatabaseConnection.ExecuteAdminQuery(query, parameters);

                if (result.Rows.Count == 0)
                {
                    LogLoginAttempt(username, "Failed", "المستخدم غير موجود", ipAddress, userAgent);
                    return false;
                }

                DataRow userRow = result.Rows[0];
                
                // Check if account is locked
                if (userRow["lockedUntil"] != DBNull.Value)
                {
                    DateTime lockedUntil = Convert.ToDateTime(userRow["lockedUntil"]);
                    if (DateTime.Now < lockedUntil)
                    {
                        LogLoginAttempt(username, "Blocked", "الحساب مقفل", ipAddress, userAgent);
                        return false;
                    }
                }

                // Verify password
                string storedPassword = userRow["password"].ToString();
                if (!VerifyPassword(password, storedPassword))
                {
                    // Increment login attempts
                    int loginAttempts = Convert.ToInt32(userRow["loginAttempts"]) + 1;
                    UpdateLoginAttempts(username, loginAttempts);
                    
                    LogLoginAttempt(username, "Failed", "كلمة مرور خاطئة", ipAddress, userAgent);
                    return false;
                }

                // Successful login
                int userId = Convert.ToInt32(userRow["id"]);
                
                // Reset login attempts and update last login
                ResetLoginAttempts(username);
                UpdateLastLogin(userId);

                // Create current user object
                CurrentUser = new AdminUser
                {
                    Id = userId,
                    Username = username,
                    FullName = userRow["fullName"].ToString(),
                    FullNameAr = userRow["fullNameAr"].ToString(),
                    Email = userRow["email"].ToString(),
                    Role = userRow["role"].ToString(),
                    LastLogin = DateTime.Now
                };

                // Log successful login
                LogLoginAttempt(username, "Success", "تسجيل دخول ناجح", ipAddress, userAgent, userId);
                LogActivity(userId, "LOGIN", "تسجيل دخول", "System", null, "Admin Panel", 
                           "Administrator logged in", "قام المدير بتسجيل الدخول", ipAddress, userAgent);

                return true;
            }
            catch (Exception ex)
            {
                LogLoginAttempt(username, "Failed", $"خطأ في النظام: {ex.Message}", ipAddress, userAgent);
                throw new Exception($"خطأ في تسجيل الدخول: {ex.Message}");
            }
        }

        // Logout method
        public static void Logout()
        {
            if (CurrentUser != null)
            {
                LogActivity(CurrentUser.Id, "LOGOUT", "تسجيل خروج", "System", null, "Admin Panel",
                           "Administrator logged out", "قام المدير بتسجيل الخروج");
                
                CurrentUser = null;
            }
        }

        // Verify password (simple comparison for now, can be enhanced with hashing)
        private static bool VerifyPassword(string inputPassword, string storedPassword)
        {
            // For now, using simple comparison
            // In production, you should use proper password hashing
            return inputPassword == storedPassword;
        }

        // Update login attempts
        private static void UpdateLoginAttempts(string username, int attempts)
        {
            string query = @"
                UPDATE admin_users 
                SET loginAttempts = @attempts,
                    lockedUntil = CASE WHEN @attempts >= 3 THEN DATEADD(MINUTE, 30, GETDATE()) ELSE NULL END
                WHERE username = @username";

            SqlParameter[] parameters = {
                new SqlParameter("@attempts", attempts),
                new SqlParameter("@username", username)
            };

            DatabaseConnection.ExecuteAdminNonQuery(query, parameters);
        }

        // Reset login attempts
        private static void ResetLoginAttempts(string username)
        {
            string query = @"
                UPDATE admin_users 
                SET loginAttempts = 0, lockedUntil = NULL
                WHERE username = @username";

            SqlParameter[] parameters = {
                new SqlParameter("@username", username)
            };

            DatabaseConnection.ExecuteAdminNonQuery(query, parameters);
        }

        // Update last login
        private static void UpdateLastLogin(int userId)
        {
            string query = @"
                UPDATE admin_users 
                SET lastLogin = GETDATE()
                WHERE id = @userId";

            SqlParameter[] parameters = {
                new SqlParameter("@userId", userId)
            };

            DatabaseConnection.ExecuteAdminNonQuery(query, parameters);
        }

        // Log login attempt
        private static void LogLoginAttempt(string username, string status, string failureReason, 
                                          string ipAddress, string userAgent, int? adminId = null)
        {
            string query = @"
                INSERT INTO admin_login_log (adminId, username, status, failureReason, ipAddress, userAgent)
                VALUES (@adminId, @username, @status, @failureReason, @ipAddress, @userAgent)";

            SqlParameter[] parameters = {
                new SqlParameter("@adminId", (object)adminId ?? DBNull.Value),
                new SqlParameter("@username", username),
                new SqlParameter("@status", status),
                new SqlParameter("@failureReason", failureReason ?? ""),
                new SqlParameter("@ipAddress", ipAddress ?? ""),
                new SqlParameter("@userAgent", userAgent ?? "")
            };

            DatabaseConnection.ExecuteAdminNonQuery(query, parameters);
        }

        // Log admin activity
        public static void LogActivity(int adminId, string action, string actionAr, string targetType,
                                     int? targetId, string targetName, string details, string detailsAr,
                                     string ipAddress = "", string userAgent = "", string severity = "Info")
        {
            string query = @"
                INSERT INTO admin_activity_log 
                (adminId, action, actionAr, targetType, targetId, targetName, details, detailsAr, 
                 ipAddress, userAgent, severity)
                VALUES 
                (@adminId, @action, @actionAr, @targetType, @targetId, @targetName, @details, @detailsAr,
                 @ipAddress, @userAgent, @severity)";

            SqlParameter[] parameters = {
                new SqlParameter("@adminId", adminId),
                new SqlParameter("@action", action),
                new SqlParameter("@actionAr", actionAr),
                new SqlParameter("@targetType", targetType),
                new SqlParameter("@targetId", (object)targetId ?? DBNull.Value),
                new SqlParameter("@targetName", targetName ?? ""),
                new SqlParameter("@details", details ?? ""),
                new SqlParameter("@detailsAr", detailsAr ?? ""),
                new SqlParameter("@ipAddress", ipAddress ?? ""),
                new SqlParameter("@userAgent", userAgent ?? ""),
                new SqlParameter("@severity", severity)
            };

            DatabaseConnection.ExecuteAdminNonQuery(query, parameters);
        }

        // Check if user has permission
        public static bool HasPermission(string permissionName)
        {
            if (CurrentUser == null) return false;

            try
            {
                string query = @"
                    SELECT COUNT(*) 
                    FROM admin_user_permissions aup
                    INNER JOIN admin_permissions ap ON aup.permissionId = ap.id
                    WHERE aup.adminId = @adminId 
                    AND ap.permissionName = @permissionName 
                    AND aup.isActive = 1 
                    AND ap.isActive = 1
                    AND (aup.expiryDate IS NULL OR aup.expiryDate > GETDATE())";

                SqlParameter[] parameters = {
                    new SqlParameter("@adminId", CurrentUser.Id),
                    new SqlParameter("@permissionName", permissionName)
                };

                DataTable result = DatabaseConnection.ExecuteAdminQuery(query, parameters);
                return Convert.ToInt32(result.Rows[0][0]) > 0;
            }
            catch
            {
                return false;
            }
        }
    }

    // Admin User class
    public class AdminUser
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string FullName { get; set; }
        public string FullNameAr { get; set; }
        public string Email { get; set; }
        public string Role { get; set; }
        public DateTime? LastLogin { get; set; }
    }
}
