-- =============================================
-- إدراج البيانات الأساسية لنظام إدارة حسابات الصيدليات
-- Insert Basic Data for Pharmacy Users Management System
-- =============================================

USE UnifiedPharmacy;
GO

PRINT 'بدء إدراج البيانات الأساسية...';

-- =============================================
-- 1. إدراج الصلاحيات الأساسية
-- Insert Basic Permissions
-- =============================================
IF NOT EXISTS (SELECT * FROM user_permissions)
BEGIN
    INSERT INTO user_permissions (permissionName, permissionNameAr, description, descriptionAr, category) VALUES
    -- إدارة المخزون
    ('INVENTORY_VIEW', 'عرض المخزون', 'View inventory items and stock levels', 'عرض عناصر المخزون ومستويات المخزون', 'Inventory'),
    ('INVENTORY_ADD', 'إضافة مخزون', 'Add new inventory items', 'إضافة عناصر مخزون جديدة', 'Inventory'),
    ('INVENTORY_EDIT', 'تعديل المخزون', 'Edit existing inventory items', 'تعديل عناصر المخزون الموجودة', 'Inventory'),
    ('INVENTORY_DELETE', 'حذف المخزون', 'Delete inventory items', 'حذف عناصر المخزون', 'Inventory'),
    
    -- إدارة المبيعات
    ('SALES_VIEW', 'عرض المبيعات', 'View sales transactions and reports', 'عرض معاملات المبيعات والتقارير', 'Sales'),
    ('SALES_CREATE', 'إنشاء مبيعات', 'Create new sales transactions', 'إنشاء معاملات مبيعات جديدة', 'Sales'),
    ('SALES_EDIT', 'تعديل المبيعات', 'Edit sales transactions', 'تعديل معاملات المبيعات', 'Sales'),
    ('SALES_DELETE', 'حذف المبيعات', 'Delete sales transactions', 'حذف معاملات المبيعات', 'Sales'),
    ('SALES_REFUND', 'استرداد المبيعات', 'Process sales refunds', 'معالجة استرداد المبيعات', 'Sales'),
    
    -- إدارة العملاء
    ('CUSTOMERS_VIEW', 'عرض العملاء', 'View customer information', 'عرض معلومات العملاء', 'Customers'),
    ('CUSTOMERS_ADD', 'إضافة عملاء', 'Add new customers', 'إضافة عملاء جدد', 'Customers'),
    ('CUSTOMERS_EDIT', 'تعديل العملاء', 'Edit customer information', 'تعديل معلومات العملاء', 'Customers'),
    ('CUSTOMERS_DELETE', 'حذف العملاء', 'Delete customers', 'حذف العملاء', 'Customers'),
    
    -- التقارير
    ('REPORTS_VIEW', 'عرض التقارير', 'View all reports', 'عرض جميع التقارير', 'Reports'),
    ('REPORTS_EXPORT', 'تصدير التقارير', 'Export reports to files', 'تصدير التقارير إلى ملفات', 'Reports'),
    ('REPORTS_FINANCIAL', 'التقارير المالية', 'View financial reports', 'عرض التقارير المالية', 'Reports'),
    
    -- إدارة المستخدمين
    ('USERS_VIEW', 'عرض المستخدمين', 'View pharmacy users', 'عرض مستخدمي الصيدلية', 'Users'),
    ('USERS_ADD', 'إضافة مستخدمين', 'Add new pharmacy users', 'إضافة مستخدمين جدد للصيدلية', 'Users'),
    ('USERS_EDIT', 'تعديل المستخدمين', 'Edit pharmacy users', 'تعديل مستخدمي الصيدلية', 'Users'),
    ('USERS_DELETE', 'حذف المستخدمين', 'Delete pharmacy users', 'حذف مستخدمي الصيدلية', 'Users'),
    ('USERS_PERMISSIONS', 'إدارة الصلاحيات', 'Manage user permissions', 'إدارة صلاحيات المستخدمين', 'Users'),
    
    -- إعدادات النظام
    ('SETTINGS_VIEW', 'عرض الإعدادات', 'View system settings', 'عرض إعدادات النظام', 'Settings'),
    ('SETTINGS_EDIT', 'تعديل الإعدادات', 'Edit system settings', 'تعديل إعدادات النظام', 'Settings'),
    ('BACKUP_CREATE', 'إنشاء نسخ احتياطية', 'Create database backups', 'إنشاء نسخ احتياطية من قاعدة البيانات', 'Settings'),
    ('BACKUP_RESTORE', 'استعادة النسخ الاحتياطية', 'Restore database backups', 'استعادة النسخ الاحتياطية لقاعدة البيانات', 'Settings');
    
    PRINT '✅ تم إدراج الصلاحيات الأساسية';
END

-- =============================================
-- 2. إدراج الأدوار الأساسية
-- Insert Basic Roles
-- =============================================
IF NOT EXISTS (SELECT * FROM user_roles)
BEGIN
    INSERT INTO user_roles (roleName, roleNameAr, description, descriptionAr, permissions) VALUES
    (
        'PharmacyManager', 
        'مدير الصيدلية', 
        'Full access to all pharmacy operations and management', 
        'وصول كامل لجميع عمليات وإدارة الصيدلية',
        '["INVENTORY_VIEW","INVENTORY_ADD","INVENTORY_EDIT","INVENTORY_DELETE","SALES_VIEW","SALES_CREATE","SALES_EDIT","SALES_DELETE","SALES_REFUND","CUSTOMERS_VIEW","CUSTOMERS_ADD","CUSTOMERS_EDIT","CUSTOMERS_DELETE","REPORTS_VIEW","REPORTS_EXPORT","REPORTS_FINANCIAL","USERS_VIEW","USERS_ADD","USERS_EDIT","USERS_DELETE","USERS_PERMISSIONS","SETTINGS_VIEW","SETTINGS_EDIT","BACKUP_CREATE","BACKUP_RESTORE"]'
    ),
    (
        'Pharmacist', 
        'صيدلي', 
        'Access to sales, inventory, and customer management', 
        'وصول لإدارة المبيعات والمخزون والعملاء',
        '["INVENTORY_VIEW","INVENTORY_ADD","INVENTORY_EDIT","SALES_VIEW","SALES_CREATE","SALES_EDIT","SALES_REFUND","CUSTOMERS_VIEW","CUSTOMERS_ADD","CUSTOMERS_EDIT","REPORTS_VIEW"]'
    ),
    (
        'SalesAssistant', 
        'مساعد مبيعات', 
        'Access to sales operations and basic inventory viewing', 
        'وصول لعمليات المبيعات وعرض المخزون الأساسي',
        '["INVENTORY_VIEW","SALES_VIEW","SALES_CREATE","CUSTOMERS_VIEW","CUSTOMERS_ADD"]'
    ),
    (
        'Cashier', 
        'أمين الصندوق', 
        'Access to sales transactions and customer management', 
        'وصول لمعاملات المبيعات وإدارة العملاء',
        '["SALES_VIEW","SALES_CREATE","CUSTOMERS_VIEW","CUSTOMERS_ADD","REPORTS_VIEW"]'
    ),
    (
        'InventoryClerk', 
        'موظف المخزون', 
        'Access to inventory management and basic reporting', 
        'وصول لإدارة المخزون والتقارير الأساسية',
        '["INVENTORY_VIEW","INVENTORY_ADD","INVENTORY_EDIT","REPORTS_VIEW"]'
    );
    
    PRINT '✅ تم إدراج الأدوار الأساسية';
END

-- =============================================
-- 3. إنشاء حسابات تجريبية للصيدليات الموجودة
-- Create Sample Accounts for Existing Pharmacies
-- =============================================
DECLARE @pharmacyCount INT;
SELECT @pharmacyCount = COUNT(*) FROM pharmacies;

IF @pharmacyCount > 0 AND NOT EXISTS (SELECT * FROM pharmacy_users)
BEGIN
    DECLARE @pharmacyId INT, @managerRoleId INT, @pharmacistRoleId INT;
    
    -- الحصول على معرفات الأدوار
    SELECT @managerRoleId = id FROM user_roles WHERE roleName = 'PharmacyManager';
    SELECT @pharmacistRoleId = id FROM user_roles WHERE roleName = 'Pharmacist';
    
    -- إنشاء حسابات للصيدليات الموجودة
    DECLARE pharmacy_cursor CURSOR FOR 
    SELECT id FROM pharmacies WHERE isActive = 1;
    
    OPEN pharmacy_cursor;
    FETCH NEXT FROM pharmacy_cursor INTO @pharmacyId;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        -- إنشاء حساب مدير الصيدلية
        INSERT INTO pharmacy_users (
            pharmacyId, username, password, fullName, fullNameAr, 
            email, phone, roleId, isActive, createdDate
        )
        SELECT 
            @pharmacyId,
            'manager' + CAST(@pharmacyId AS NVARCHAR),
            'Manager@123',
            'Pharmacy Manager',
            'مدير الصيدلية',
            'manager' + CAST(@pharmacyId AS NVARCHAR) + '@' + REPLACE(LOWER(pharmacyName), ' ', '') + '.com',
            phone,
            @managerRoleId,
            1,
            GETDATE()
        FROM pharmacies WHERE id = @pharmacyId;
        
        -- إنشاء حساب صيدلي
        INSERT INTO pharmacy_users (
            pharmacyId, username, password, fullName, fullNameAr, 
            email, phone, roleId, isActive, createdDate
        )
        SELECT 
            @pharmacyId,
            'pharmacist' + CAST(@pharmacyId AS NVARCHAR),
            'Pharmacist@123',
            'Main Pharmacist',
            'الصيدلي الرئيسي',
            'pharmacist' + CAST(@pharmacyId AS NVARCHAR) + '@' + REPLACE(LOWER(pharmacyName), ' ', '') + '.com',
            phone,
            @pharmacistRoleId,
            1,
            GETDATE()
        FROM pharmacies WHERE id = @pharmacyId;
        
        FETCH NEXT FROM pharmacy_cursor INTO @pharmacyId;
    END
    
    CLOSE pharmacy_cursor;
    DEALLOCATE pharmacy_cursor;
    
    PRINT '✅ تم إنشاء حسابات تجريبية للصيدليات الموجودة';
END

PRINT '';
PRINT '🎉 تم إدراج جميع البيانات الأساسية بنجاح!';
PRINT '';
PRINT '📋 ملخص الحسابات المنشأة:';
SELECT 
    p.pharmacyNameAr as [اسم الصيدلية],
    pu.username as [اسم المستخدم],
    pu.fullNameAr as [الاسم الكامل],
    ur.roleNameAr as [الدور],
    pu.password as [كلمة المرور]
FROM pharmacy_users pu
INNER JOIN pharmacies p ON pu.pharmacyId = p.id
INNER JOIN user_roles ur ON pu.roleId = ur.id
ORDER BY p.pharmacyNameAr, ur.roleName;
