# 🏥 نظام إدارة حسابات الصيدليات
## Pharmacy Users Management System

### 📋 نظرة عامة
تم إضافة نظام شامل لإدارة حسابات الصيدليات يتيح:
- إدارة مستخدمي الصيدليات (مديرين وموظفين)
- نظام أدوار وصلاحيات متقدم
- تسجيل دخول منفصل لكل صيدلية
- تتبع الأنشطة والجلسات

---

## 🚀 خطوات التشغيل

### الخطوة 1: إعداد قاعدة البيانات
```sql
-- 1. تنفيذ ملف إنشاء النظام الأساسي
SETUP_COMPLETE_PHARMACY_SYSTEM.sql

-- 2. تنفيذ ملف إدراج البيانات الأساسية
INSERT_PHARMACY_USERS_DATA.sql
```

### الخطوة 2: بناء المشروع
1. افتح Visual Studio
2. اضغط `Ctrl+Shift+B` لبناء المشروع
3. تأكد من عدم وجود أخطاء

### الخطوة 3: تشغيل النظام
1. اضغط `F5` لتشغيل النظام
2. سجل دخول بحساب المدير المركزي:
   - **اسم المستخدم:** `admin`
   - **كلمة المرور:** `Admin@123`
3. اضغط على زر **"حسابات الصيدليات"**

---

## 🎯 الميزات الجديدة

### 1. صفحة عرض حسابات الصيدليات
- **المسار:** حسابات الصيدليات
- **الوظائف:**
  - عرض جميع مستخدمي الصيدليات
  - البحث والتصفية حسب الصيدلية والدور والحالة
  - إضافة/تعديل/حذف المستخدمين

### 2. نظام الأدوار والصلاحيات
#### الأدوار المتاحة:
- **مدير الصيدلية** (PharmacyManager): صلاحيات كاملة
- **صيدلي** (Pharmacist): إدارة المبيعات والمخزون
- **مساعد مبيعات** (SalesAssistant): عمليات المبيعات الأساسية
- **أمين الصندوق** (Cashier): معاملات المبيعات والعملاء
- **موظف المخزون** (InventoryClerk): إدارة المخزون

#### الصلاحيات المتاحة:
- **المخزون:** عرض، إضافة، تعديل، حذف
- **المبيعات:** عرض، إنشاء، تعديل، حذف، استرداد
- **العملاء:** عرض، إضافة، تعديل، حذف
- **التقارير:** عرض، تصدير، تقارير مالية
- **المستخدمين:** عرض، إضافة، تعديل، حذف، إدارة الصلاحيات
- **الإعدادات:** عرض، تعديل، نسخ احتياطية

### 3. الحسابات التجريبية
يتم إنشاء حسابات تجريبية تلقائياً لكل صيدلية:

#### صيدلية النور:
- **مدير:** `manager1` / `Manager@123`
- **صيدلي:** `pharmacist1` / `Pharmacist@123`

#### صيدلية الشفاء:
- **مدير:** `manager2` / `Manager@123`
- **صيدلي:** `pharmacist2` / `Pharmacist@123`

#### صيدلية الحياة:
- **مدير:** `manager3` / `Manager@123`
- **صيدلي:** `pharmacist3` / `Pharmacist@123`

---

## 🗄️ هيكل قاعدة البيانات

### الجداول الجديدة:
1. **user_roles** - أدوار المستخدمين
2. **pharmacy_users** - مستخدمو الصيدليات
3. **user_permissions** - صلاحيات النظام
4. **user_permission_mapping** - ربط المستخدمين بالصلاحيات
5. **user_sessions** - جلسات المستخدمين
6. **user_activity_log** - سجل الأنشطة

### العلاقات:
- `pharmacy_users.pharmacyId` → `pharmacies.id`
- `pharmacy_users.roleId` → `user_roles.id`
- `user_permission_mapping.userId` → `pharmacy_users.id`
- `user_permission_mapping.permissionId` → `user_permissions.id`

---

## 🔧 الملفات الجديدة

### ملفات قاعدة البيانات:
- `SETUP_COMPLETE_PHARMACY_SYSTEM.sql` - إعداد النظام الكامل
- `CREATE_PHARMACY_USERS_SYSTEM.sql` - إنشاء جداول المستخدمين
- `INSERT_PHARMACY_USERS_DATA.sql` - إدراج البيانات الأساسية

### ملفات الكود:
- `PharmacyUser.cs` - فئة بيانات المستخدم
- `PharmacyUserManager.cs` - إدارة عمليات المستخدمين
- `forma/PharmacyUsersControl.cs` - صفحة عرض المستخدمين
- `forma/AddEditPharmacyUserForm.cs` - نموذج إضافة/تعديل المستخدم

---

## 🎨 واجهة المستخدم

### صفحة عرض المستخدمين:
- **البحث:** بالاسم أو اسم المستخدم
- **التصفية:** حسب الصيدلية، الدور، الحالة
- **الأزرار:** إضافة، تعديل، حذف، تحديث

### نموذج إضافة/تعديل المستخدم:
- **المعلومات الأساسية:** الاسم، اسم المستخدم، البريد الإلكتروني
- **معلومات الاتصال:** الهاتف، الجوال
- **الإعدادات:** الصيدلية، الدور، الحالة
- **الأمان:** كلمة المرور وتأكيدها

---

## 🔐 الأمان والحماية

### تشفير كلمات المرور:
- حالياً: مقارنة مباشرة (للتطوير)
- مستقبلاً: تشفير BCrypt أو SHA256

### إدارة الجلسات:
- رموز جلسة فريدة
- انتهاء صلاحية تلقائي (8 ساعات)
- تتبع عناوين IP ومعلومات المتصفح

### قفل الحسابات:
- قفل تلقائي بعد 5 محاولات فاشلة
- مدة القفل: 30 دقيقة
- إعادة تعيين تلقائي عند النجاح

---

## 📊 التقارير والإحصائيات

### سجل الأنشطة:
- تسجيل جميع العمليات
- معلومات المستخدم والوقت
- تفاصيل التغييرات (قديم/جديد)

### إحصائيات المستخدمين:
- عدد المستخدمين لكل صيدلية
- توزيع الأدوار
- آخر تسجيل دخول
- حالة الحسابات

---

## 🚨 استكشاف الأخطاء

### مشاكل شائعة:
1. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من connection string
   - تأكد من تشغيل SQL Server

2. **جداول غير موجودة**
   - نفذ ملف `SETUP_COMPLETE_PHARMACY_SYSTEM.sql`

3. **لا توجد بيانات**
   - نفذ ملف `INSERT_PHARMACY_USERS_DATA.sql`

4. **أخطاء في البناء**
   - تحقق من مراجع Guna.UI2
   - تأكد من إضافة جميع الملفات للمشروع

---

## 🔄 التطويرات المستقبلية

### المرحلة التالية:
- [ ] نظام تسجيل دخول منفصل للصيدليات
- [ ] لوحة تحكم خاصة بكل صيدلية
- [ ] إدارة المخزون والمبيعات
- [ ] تقارير مفصلة لكل صيدلية
- [ ] نظام إشعارات
- [ ] تطبيق جوال

### تحسينات الأمان:
- [ ] تشفير كلمات المرور
- [ ] مصادقة ثنائية العامل
- [ ] SSL/TLS للاتصالات
- [ ] تدقيق أمني شامل

---

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. تحقق من ملفات السجل
2. راجع قاعدة البيانات
3. تأكد من صحة الإعدادات
4. اتصل بفريق التطوير

---

**تم إنشاء النظام بواسطة:** Augment Agent  
**التاريخ:** يوليو 2024  
**الإصدار:** 1.0.0
