# 🚨 إصلاح عاجل لمشكلة إضافة الصيدليات - قاعدة البيانات UnifiedPharmacy

## المشكلة
- صفحة قائمة الصيدليات تعمل ✅
- إضافة صيدلية لا تعمل ❌
- اسم قاعدة البيانات الصحيح هو `UnifiedPharmacy` وليس `pharmacy`

## السبب
أعمدة مفقودة في جدول `pharmacies` في قاعدة البيانات `UnifiedPharmacy`

## الحل السريع

### 1. افتح SQL Server Management Studio

### 2. تحقق من وجود قاعدة البيانات:
```sql
-- تحقق من وجود قاعدة البيانات
SELECT name FROM sys.databases WHERE name = 'UnifiedPharmacy'
```

### 3. إذا لم تكن موجودة، أنشئها:
```sql
CREATE DATABASE UnifiedPharmacy COLLATE Arabic_CI_AS;
```

### 4. نفذ ملف الإصلاح:
**افتح ملف `Fix_UnifiedPharmacy_Columns.sql` ونفذه كاملاً**

### 5. أو نفذ الاستعلام التالي مباشرة:
```sql
USE UnifiedPharmacy;
GO

-- إنشاء الجدول إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    CREATE TABLE pharmacies (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyName NVARCHAR(100) NOT NULL,
        pharmacyNameAr NVARCHAR(100) NOT NULL,
        ownerName NVARCHAR(100) NOT NULL,
        ownerNameAr NVARCHAR(100) NOT NULL,
        licenseNumber NVARCHAR(50) UNIQUE NOT NULL,
        taxNumber NVARCHAR(50),
        email NVARCHAR(100) UNIQUE NOT NULL,
        phone NVARCHAR(20) NOT NULL,
        mobile NVARCHAR(20),
        address NVARCHAR(200) NOT NULL,
        addressAr NVARCHAR(200) NOT NULL,
        city NVARCHAR(50) NOT NULL,
        cityAr NVARCHAR(50) NOT NULL,
        region NVARCHAR(50),
        regionAr NVARCHAR(50),
        postalCode NVARCHAR(10),
        latitude DECIMAL(10,8),
        longitude DECIMAL(11,8),
        status NVARCHAR(20) NOT NULL DEFAULT 'Pending',
        isActive BIT NOT NULL DEFAULT 1,
        registrationDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        approvalDate DATETIME2,
        approvedBy INT,
        lastActivityDate DATETIME2,
        subscriptionStatus NVARCHAR(20) NOT NULL DEFAULT 'Trial',
        subscriptionStartDate DATETIME2 DEFAULT GETDATE(),
        subscriptionEndDate DATETIME2,
        notes NVARCHAR(300)
    );
    PRINT 'تم إنشاء جدول pharmacies';
END

-- إضافة الأعمدة المفقودة إذا كان الجدول موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionStatus')
    ALTER TABLE pharmacies ADD subscriptionStatus NVARCHAR(20) NOT NULL DEFAULT 'Trial';

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'isActive')
    ALTER TABLE pharmacies ADD isActive BIT NOT NULL DEFAULT 1;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'registrationDate')
    ALTER TABLE pharmacies ADD registrationDate DATETIME2 NOT NULL DEFAULT GETDATE();

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'notes')
    ALTER TABLE pharmacies ADD notes NVARCHAR(300);

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'taxNumber')
    ALTER TABLE pharmacies ADD taxNumber NVARCHAR(50);

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'mobile')
    ALTER TABLE pharmacies ADD mobile NVARCHAR(20);

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'region')
    ALTER TABLE pharmacies ADD region NVARCHAR(50);

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'regionAr')
    ALTER TABLE pharmacies ADD regionAr NVARCHAR(50);

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'postalCode')
    ALTER TABLE pharmacies ADD postalCode NVARCHAR(10);

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'approvalDate')
    ALTER TABLE pharmacies ADD approvalDate DATETIME2;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'approvedBy')
    ALTER TABLE pharmacies ADD approvedBy INT;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'lastActivityDate')
    ALTER TABLE pharmacies ADD lastActivityDate DATETIME2;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionStartDate')
    ALTER TABLE pharmacies ADD subscriptionStartDate DATETIME2 DEFAULT GETDATE();

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionEndDate')
    ALTER TABLE pharmacies ADD subscriptionEndDate DATETIME2;

PRINT 'تم إصلاح جدول الصيدليات بنجاح!';
```

## التحقق من الإصلاح

### 1. تحقق من بنية الجدول:
```sql
USE UnifiedPharmacy;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'pharmacies'
ORDER BY ORDINAL_POSITION;
```

### 2. تحقق من إمكانية الإدراج:
```sql
-- اختبار إدراج صيدلية تجريبية
INSERT INTO pharmacies (
    pharmacyName, pharmacyNameAr, ownerName, ownerNameAr, 
    licenseNumber, email, phone, address, addressAr, city, cityAr
) VALUES (
    'Test Pharmacy', 'صيدلية الاختبار', 'Test Owner', 'مالك الاختبار',
    'TEST001', '<EMAIL>', '**********', 
    'Test Address', 'عنوان الاختبار', 'Riyadh', 'الرياض'
);

-- تحقق من الإدراج
SELECT * FROM pharmacies WHERE licenseNumber = 'TEST001';
```

## اختبار النظام

### 1. شغل البرنامج
### 2. سجل دخول: `admin` / `Admin@123`
### 3. اذهب إلى "إضافة صيدلية"
### 4. املأ البيانات واضغط "حفظ"
### 5. يجب أن تظهر رسالة نجاح

## إذا استمرت المشكلة

### تحقق من:
1. ✅ تشغيل SQL Server
2. ✅ وجود قاعدة البيانات `UnifiedPharmacy`
3. ✅ وجود جدول `pharmacies` مع جميع الأعمدة
4. ✅ صلاحيات الوصول لقاعدة البيانات

### رسائل الخطأ الشائعة:
- **"Invalid column name"** → نفذ ملف الإصلاح
- **"Cannot open database"** → تحقق من اسم قاعدة البيانات
- **"Invalid object name 'pharmacies'"** → أنشئ الجدول

---
**بعد تطبيق هذا الإصلاح، ستعمل إضافة الصيدليات بشكل مثالي! 🚀**
