-- =============================================
-- إصلاح طارئ لقاعدة البيانات UnifiedPharmacy
-- Emergency Fix for UnifiedPharmacy Database
-- =============================================

USE UnifiedPharmacy;
GO

PRINT '🚨 بدء الإصلاح الطارئ لقاعدة البيانات...';

-- حذف الجدول القديم تماماً وإعادة إنشاؤه
IF EXISTS (SELECT * FROM sysobjects WHERE name='pharmacies' AND xtype='U')
BEGIN
    DROP TABLE pharmacies;
    PRINT '✅ تم حذف الجدول القديم';
END

-- إنشاء الجدول بالبنية الصحيحة والكاملة
CREATE TABLE pharmacies (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyName NVARCHAR(100) NOT NULL,
    pharmacyNameAr NVARCHAR(100) NOT NULL,
    ownerName NVARCHAR(100) NOT NULL,
    ownerNameAr NVARCHAR(100) NOT NULL,
    licenseNumber NVARCHAR(50) UNIQUE NOT NULL,
    taxNumber NVARCHAR(50),
    email NVARCHAR(100) UNIQUE NOT NULL,
    phone NVARCHAR(20) NOT NULL,
    mobile NVARCHAR(20),
    address NVARCHAR(200) NOT NULL,
    addressAr NVARCHAR(200) NOT NULL,
    city NVARCHAR(50) NOT NULL,
    cityAr NVARCHAR(50) NOT NULL,
    region NVARCHAR(50),
    regionAr NVARCHAR(50),
    postalCode NVARCHAR(10),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    status NVARCHAR(20) NOT NULL DEFAULT 'Pending',
    isActive BIT NOT NULL DEFAULT 1,
    registrationDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    approvalDate DATETIME2,
    approvedBy INT,
    lastActivityDate DATETIME2,
    subscriptionStatus NVARCHAR(20) NOT NULL DEFAULT 'Trial',
    subscriptionStartDate DATETIME2 DEFAULT GETDATE(),
    subscriptionEndDate DATETIME2,
    notes NVARCHAR(300)
);

PRINT '✅ تم إنشاء جدول pharmacies بالبنية الصحيحة';

-- إدراج بيانات تجريبية كاملة
INSERT INTO pharmacies (
    pharmacyName, pharmacyNameAr, ownerName, ownerNameAr, licenseNumber, taxNumber,
    email, phone, mobile, address, addressAr, city, cityAr, region, regionAr,
    postalCode, latitude, longitude, status, subscriptionStatus, notes
) VALUES
(
    'Al-Noor Pharmacy', 'صيدلية النور', 'Ahmed Mohammed Ali', 'أحمد محمد علي', 
    'LIC001', 'TAX001', '<EMAIL>', '**********', '**********', 
    'King Fahd Road, Al-Noor District', 'شارع الملك فهد، حي النور',
    'Riyadh', 'الرياض', 'Riyadh', 'الرياض', '12345', 
    24.7136, 46.6753, 'Approved', 'Premium', 'صيدلية رئيسية في حي النور'
),
(
    'Al-Shifa Pharmacy', 'صيدلية الشفاء', 'Fatima Ahmed', 'فاطمة أحمد', 
    'LIC002', 'TAX002', '<EMAIL>', '**********', '**********', 
    'King Abdulaziz Road, Al-Malaz District', 'طريق الملك عبدالعزيز، حي الملز',
    'Riyadh', 'الرياض', 'Riyadh', 'الرياض', '12346', 
    24.6408, 46.7728, 'Approved', 'Basic', 'صيدلية متخصصة في الأدوية العامة'
),
(
    'Al-Hayat Pharmacy', 'صيدلية الحياة', 'Mohammed Salem', 'محمد سالم', 
    'LIC003', 'TAX003', '<EMAIL>', '0122345678', '0501234569', 
    'Tahlia Street, Al-Salamah District', 'شارع التحلية، حي السلامة',
    'Jeddah', 'جدة', 'Makkah', 'مكة المكرمة', '21589', 
    21.5810, 39.1653, 'Pending', 'Trial', 'صيدلية حديثة في جدة'
),
(
    'Al-Amal Pharmacy', 'صيدلية الأمل', 'Sara Abdullah', 'سارة عبدالله', 
    'LIC004', 'TAX004', '<EMAIL>', '0133456789', '0501234570', 
    'Prince Mohammed Street', 'شارع الأمير محمد',
    'Dammam', 'الدمام', 'Eastern Province', 'المنطقة الشرقية', '31411', 
    26.4207, 50.0888, 'Approved', 'Enterprise', 'صيدلية متطورة في الدمام'
),
(
    'Al-Salam Pharmacy', 'صيدلية السلام', 'Omar Hassan', 'عمر حسن', 
    'LIC005', 'TAX005', '<EMAIL>', '0144567890', '0501234571', 
    'University Street', 'شارع الجامعة',
    'Medina', 'المدينة المنورة', 'Medina', 'المدينة المنورة', '42311', 
    24.4686, 39.6142, 'Suspended', 'Expired', 'صيدلية قريبة من الجامعة'
);

PRINT '✅ تم إدراج 5 صيدليات تجريبية';

-- تحديث تواريخ الاشتراك
UPDATE pharmacies 
SET subscriptionStartDate = GETDATE(),
    subscriptionEndDate = CASE 
        WHEN subscriptionStatus = 'Trial' THEN DATEADD(MONTH, 1, GETDATE())
        WHEN subscriptionStatus = 'Basic' THEN DATEADD(MONTH, 6, GETDATE())
        WHEN subscriptionStatus = 'Premium' THEN DATEADD(YEAR, 1, GETDATE())
        WHEN subscriptionStatus = 'Enterprise' THEN DATEADD(YEAR, 2, GETDATE())
        ELSE DATEADD(MONTH, -1, GETDATE()) -- Expired
    END,
    lastActivityDate = GETDATE();

PRINT '✅ تم تحديث تواريخ الاشتراك';

-- عرض النتائج
PRINT '';
PRINT '📊 نتائج الإصلاح:';

-- عرض بنية الجدول
SELECT 
    ROW_NUMBER() OVER (ORDER BY ORDINAL_POSITION) as [#],
    COLUMN_NAME as [اسم العمود],
    DATA_TYPE as [نوع البيانات],
    IS_NULLABLE as [يقبل NULL]
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'pharmacies'
ORDER BY ORDINAL_POSITION;

-- عرض عدد الصيدليات
DECLARE @count INT;
SELECT @count = COUNT(*) FROM pharmacies;
PRINT '';
PRINT '📈 عدد الصيدليات: ' + CAST(@count AS NVARCHAR);

-- عرض عينة من البيانات
PRINT '';
PRINT '📄 عينة من البيانات:';
SELECT 
    id,
    pharmacyName,
    pharmacyNameAr,
    ownerName,
    ownerNameAr,
    licenseNumber,
    email,
    phone,
    city,
    cityAr,
    status,
    subscriptionStatus
FROM pharmacies
ORDER BY id;

-- اختبار الاستعلام المستخدم في البرنامج
BEGIN TRY
    DECLARE @testResult TABLE (
        id INT,
        pharmacyName NVARCHAR(100),
        pharmacyNameAr NVARCHAR(100),
        ownerName NVARCHAR(100),
        ownerNameAr NVARCHAR(100)
    );
    
    INSERT INTO @testResult
    SELECT TOP 1 id, pharmacyName, pharmacyNameAr, ownerName, ownerNameAr
    FROM pharmacies;
    
    PRINT '';
    PRINT '✅ اختبار الاستعلام نجح - البرنامج سيعمل الآن!';
END TRY
BEGIN CATCH
    PRINT '';
    PRINT '❌ فشل اختبار الاستعلام: ' + ERROR_MESSAGE();
END CATCH

PRINT '';
PRINT '🎉 تم الانتهاء من الإصلاح الطارئ بنجاح!';
PRINT '🚀 يمكنك الآن استخدام البرنامج بدون مشاكل';

-- تعليمات للمستخدم
PRINT '';
PRINT '📋 التعليمات التالية:';
PRINT '1. أعد تشغيل البرنامج';
PRINT '2. سجل دخول: admin / Admin@123';
PRINT '3. اذهب إلى قائمة الصيدليات';
PRINT '4. يجب أن تظهر 5 صيدليات تجريبية';
PRINT '5. جرب إضافة صيدلية جديدة';
PRINT '6. جرب تعديل أي صيدلية موجودة';
