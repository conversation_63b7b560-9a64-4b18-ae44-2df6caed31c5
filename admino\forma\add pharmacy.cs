﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace admino.forma
{
    public partial class add_pharmacy : UserControl
    {
        private PharmacyInfo currentPharmacy;
        private bool isEditMode = false;

        public add_pharmacy()
        {
            InitializeComponent();
            InitializeCustomComponents();

            // Ensure button events are connected
            btnSave.Click += btnSave_Click;
            btnClear.Click += btnClear_Click;

            // Add load event to ensure proper sizing
            this.Load += Add_pharmacy_Load;
        }

        private void Add_pharmacy_Load(object sender, EventArgs e)
        {
            // Test database connection first
            try
            {
                if (!DatabaseConnection.TestPharmacyConnection())
                {
                    MessageBox.Show("خطأ في الاتصال بقاعدة بيانات الصيدلية.\n" +
                                  "تحقق من:\n" +
                                  "1. تشغيل SQL Server\n" +
                                  "2. وجود قاعدة البيانات 'UnifiedPharmacy'\n" +
                                  "3. تنفيذ ملف Fix_UnifiedPharmacy_Columns.sql",
                                  "خطأ في الاتصال", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الاتصال: {ex.Message}",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Ensure the control fills the parent container
            if (this.Parent != null)
            {
                this.Dock = DockStyle.Fill;
                this.Size = this.Parent.Size;

                // Update panel size to match
                guna2Panel1.Width = this.Width - 40;
                guna2Panel1.Height = this.Height - 100;

                // Re-apply layout with new dimensions
                SetupControlLayout();
            }
        }

        public add_pharmacy(PharmacyInfo pharmacy) : this()
        {
            currentPharmacy = pharmacy;
            isEditMode = true;
            LoadPharmacyData();
        }

        private void InitializeCustomComponents()
        {
            // Set right-to-left for Arabic support
            this.RightToLeft = RightToLeft.Yes;

            // Initialize status options
            cmbStatus.Items.AddRange(new string[] { "Pending", "Approved", "Rejected", "Suspended" });
            cmbStatus.SelectedIndex = 0;

            // Initialize subscription status options
            cmbSubscriptionStatus.Items.AddRange(new string[] { "Trial", "Basic", "Premium", "Enterprise" });
            cmbSubscriptionStatus.SelectedIndex = 0;

            // Setup control layout
            SetupControlLayout();

            // Setup labels and their properties - temporarily disabled
            // SetupBasicProperties();
        }

        private void SetupControlLayout()
        {
            // تخطيط محسن لاستغلال كامل الشاشة - 3 أعمدة
            int col1X = 30;   // العمود الأول
            int col2X = 380;  // العمود الثاني
            int col3X = 730;  // العمود الثالث
            int fieldWidth = 320;
            int fieldHeight = 35;
            int labelHeight = 20;
            int rowSpacing = 65;

            // الصف الأول: أسماء الصيدلية
            int row1Y = 20;
            txtPharmacyName.Location = new System.Drawing.Point(col1X, row1Y + labelHeight);
            txtPharmacyName.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtPharmacyNameAr.Location = new System.Drawing.Point(col2X, row1Y + labelHeight);
            txtPharmacyNameAr.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtPharmacyNameAr.RightToLeft = RightToLeft.Yes;

            // الصف الثاني: أسماء المالك
            int row2Y = row1Y + rowSpacing;
            txtOwnerName.Location = new System.Drawing.Point(col1X, row2Y + labelHeight);
            txtOwnerName.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtOwnerNameAr.Location = new System.Drawing.Point(col2X, row2Y + labelHeight);
            txtOwnerNameAr.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtOwnerNameAr.RightToLeft = RightToLeft.Yes;

            // الصف الثالث: الترخيص والضريبة
            int row3Y = row2Y + rowSpacing;
            txtLicenseNumber.Location = new System.Drawing.Point(col1X, row3Y + labelHeight);
            txtLicenseNumber.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtTaxNumber.Location = new System.Drawing.Point(col2X, row3Y + labelHeight);
            txtTaxNumber.Size = new System.Drawing.Size(fieldWidth, fieldHeight);

            // الصف الرابع: معلومات الاتصال
            int row4Y = row3Y + rowSpacing;
            txtEmail.Location = new System.Drawing.Point(col1X, row4Y + labelHeight);
            txtEmail.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtPhone.Location = new System.Drawing.Point(col2X, row4Y + labelHeight);
            txtPhone.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtMobile.Location = new System.Drawing.Point(col3X, row4Y + labelHeight);
            txtMobile.Size = new System.Drawing.Size(fieldWidth, fieldHeight);

            // الصف الخامس: العناوين
            int row5Y = row4Y + rowSpacing;
            txtAddress.Location = new System.Drawing.Point(col1X, row5Y + labelHeight);
            txtAddress.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtAddressAr.Location = new System.Drawing.Point(col2X, row5Y + labelHeight);
            txtAddressAr.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtAddressAr.RightToLeft = RightToLeft.Yes;

            // الصف السادس: المدن
            int row6Y = row5Y + rowSpacing;
            txtCity.Location = new System.Drawing.Point(col1X, row6Y + labelHeight);
            txtCity.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtCityAr.Location = new System.Drawing.Point(col2X, row6Y + labelHeight);
            txtCityAr.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtCityAr.RightToLeft = RightToLeft.Yes;

            // الصف السابع: المناطق والرمز البريدي
            int row7Y = row6Y + rowSpacing;
            txtRegion.Location = new System.Drawing.Point(col1X, row7Y + labelHeight);
            txtRegion.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtRegionAr.Location = new System.Drawing.Point(col2X, row7Y + labelHeight);
            txtRegionAr.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtRegionAr.RightToLeft = RightToLeft.Yes;
            txtPostalCode.Location = new System.Drawing.Point(col3X, row7Y + labelHeight);
            txtPostalCode.Size = new System.Drawing.Size(fieldWidth, fieldHeight);

            // الصف الثامن: الإحداثيات
            int row8Y = row7Y + rowSpacing;
            txtLatitude.Location = new System.Drawing.Point(col1X, row8Y + labelHeight);
            txtLatitude.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            txtLongitude.Location = new System.Drawing.Point(col2X, row8Y + labelHeight);
            txtLongitude.Size = new System.Drawing.Size(fieldWidth, fieldHeight);

            // الصف التاسع: الحالة والاشتراك
            int row9Y = row8Y + rowSpacing;
            cmbStatus.Location = new System.Drawing.Point(col1X, row9Y + labelHeight);
            cmbStatus.Size = new System.Drawing.Size(fieldWidth, fieldHeight);
            cmbSubscriptionStatus.Location = new System.Drawing.Point(col2X, row9Y + labelHeight);
            cmbSubscriptionStatus.Size = new System.Drawing.Size(fieldWidth, fieldHeight);

            // الصف العاشر: الملاحظات
            int row10Y = row9Y + rowSpacing;
            txtNotes.Location = new System.Drawing.Point(col1X, row10Y + labelHeight);
            txtNotes.Size = new System.Drawing.Size(fieldWidth * 2 + 50, 50);
            txtNotes.Multiline = true;
            txtNotes.RightToLeft = RightToLeft.Yes;

            // الأزرار في الأسفل - توسيط أفضل
            int buttonsY = row10Y + 80;
            int buttonWidth = 180;
            int buttonSpacing = 20;
            int totalButtonsWidth = (buttonWidth * 2) + buttonSpacing;
            int buttonsStartX = (guna2Panel1.Width - totalButtonsWidth) / 2;

            // تكوين زر الحفظ
            btnSave.Location = new System.Drawing.Point(buttonsStartX, buttonsY);
            btnSave.Size = new System.Drawing.Size(buttonWidth, 50);
            btnSave.Text = "حفظ الصيدلية";
            btnSave.FillColor = System.Drawing.Color.FromArgb(46, 204, 113);
            btnSave.Visible = true;
            btnSave.Enabled = true;
            btnSave.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            btnSave.BorderRadius = 8;

            // تكوين زر المسح
            btnClear.Location = new System.Drawing.Point(buttonsStartX + buttonWidth + buttonSpacing, buttonsY);
            btnClear.Size = new System.Drawing.Size(buttonWidth, 50);
            btnClear.Text = "مسح البيانات";
            btnClear.FillColor = System.Drawing.Color.FromArgb(231, 76, 60);
            btnClear.Visible = true;
            btnClear.Enabled = true;
            btnClear.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            btnClear.BorderRadius = 8;

            // تطبيق الخصائص العامة على جميع العناصر
            ApplyCommonStyles();
            SetPlaceholders();
        }

        // طريقة SetupBasicProperties تم حذفها مؤقتاً لحل مشكلة البناء
        // سيتم إعادة إضافتها لاحقاً بعد إنشاء جميع Labels المطلوبة

        private void ApplyCommonStyles()
        {
            // تطبيق الأنماط على جميع TextBox
            foreach (Control control in guna2Panel1.Controls)
            {
                if (control is Guna.UI2.WinForms.Guna2TextBox textBox)
                {
                    textBox.BorderRadius = 8;
                    textBox.Font = new System.Drawing.Font("Segoe UI", 11F);
                    textBox.BorderColor = System.Drawing.Color.FromArgb(213, 218, 223);
                }
                else if (control is Guna.UI2.WinForms.Guna2ComboBox comboBox)
                {
                    comboBox.BorderRadius = 8;
                    comboBox.Font = new System.Drawing.Font("Segoe UI", 11F);
                    comboBox.BorderColor = System.Drawing.Color.FromArgb(213, 218, 223);
                }
                else if (control is Guna.UI2.WinForms.Guna2Button button)
                {
                    button.BorderRadius = 8;
                    button.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
                    button.ForeColor = System.Drawing.Color.White;
                }
            }
        }

        private void SetPlaceholders()
        {
            // إضافة placeholders للحقول
            txtPharmacyName.PlaceholderText = "مثال: Al-Noor Pharmacy";
            txtPharmacyNameAr.PlaceholderText = "مثال: صيدلية النور";
            txtOwnerName.PlaceholderText = "مثال: Ahmed Ali";
            txtOwnerNameAr.PlaceholderText = "مثال: أحمد علي";
            txtLicenseNumber.PlaceholderText = "مثال: LIC001";
            txtTaxNumber.PlaceholderText = "مثال: ********* (اختياري)";
            txtEmail.PlaceholderText = "مثال: <EMAIL>";
            txtPhone.PlaceholderText = "مثال: *********";
            txtMobile.PlaceholderText = "مثال: ********** (اختياري)";
            txtAddress.PlaceholderText = "مثال: King Fahd Road";
            txtAddressAr.PlaceholderText = "مثال: طريق الملك فهد";
            txtCity.PlaceholderText = "مثال: Riyadh";
            txtCityAr.PlaceholderText = "مثال: الرياض";
            txtRegion.PlaceholderText = "مثال: Central Region (اختياري)";
            txtRegionAr.PlaceholderText = "مثال: المنطقة الوسطى (اختياري)";
            txtPostalCode.PlaceholderText = "مثال: 12345 (اختياري)";
            txtLatitude.PlaceholderText = "مثال: 24.7136 (اختياري)";
            txtLongitude.PlaceholderText = "مثال: 46.6753 (اختياري)";
            txtNotes.PlaceholderText = "أي ملاحظات إضافية... (اختياري)";
        }

        private void LoadPharmacyData()
        {
            if (currentPharmacy != null)
            {
                txtPharmacyName.Text = currentPharmacy.PharmacyName;
                txtPharmacyNameAr.Text = currentPharmacy.PharmacyNameAr;
                txtOwnerName.Text = currentPharmacy.OwnerName;
                txtOwnerNameAr.Text = currentPharmacy.OwnerNameAr;
                txtLicenseNumber.Text = currentPharmacy.LicenseNumber;
                txtTaxNumber.Text = currentPharmacy.TaxNumber;
                txtEmail.Text = currentPharmacy.Email;
                txtPhone.Text = currentPharmacy.Phone;
                txtMobile.Text = currentPharmacy.Mobile;
                txtAddress.Text = currentPharmacy.Address;
                txtAddressAr.Text = currentPharmacy.AddressAr;
                txtCity.Text = currentPharmacy.City;
                txtCityAr.Text = currentPharmacy.CityAr;
                txtRegion.Text = currentPharmacy.Region;
                txtRegionAr.Text = currentPharmacy.RegionAr;
                txtPostalCode.Text = currentPharmacy.PostalCode;

                if (currentPharmacy.Latitude.HasValue)
                    txtLatitude.Text = currentPharmacy.Latitude.Value.ToString();

                if (currentPharmacy.Longitude.HasValue)
                    txtLongitude.Text = currentPharmacy.Longitude.Value.ToString();

                cmbStatus.Text = currentPharmacy.Status;
                cmbSubscriptionStatus.Text = currentPharmacy.SubscriptionStatus;
                txtNotes.Text = currentPharmacy.Notes;

                // Change button text for edit mode
                btnSave.Text = "تحديث الصيدلية";
                lblTitle.Text = "تعديل بيانات الصيدلية";
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // Debug message to confirm button click is working
                System.Diagnostics.Debug.WriteLine("btnSave_Click called - Button is working!");

                if (!ValidateInput())
                    return;

                PharmacyInfo pharmacy = new PharmacyInfo
                {
                    PharmacyName = txtPharmacyName.Text.Trim(),
                    PharmacyNameAr = txtPharmacyNameAr.Text.Trim(),
                    OwnerName = txtOwnerName.Text.Trim(),
                    OwnerNameAr = txtOwnerNameAr.Text.Trim(),
                    LicenseNumber = txtLicenseNumber.Text.Trim(),
                    TaxNumber = txtTaxNumber.Text.Trim(),
                    Email = txtEmail.Text.Trim(),
                    Phone = txtPhone.Text.Trim(),
                    Mobile = txtMobile.Text.Trim(),
                    Address = txtAddress.Text.Trim(),
                    AddressAr = txtAddressAr.Text.Trim(),
                    City = txtCity.Text.Trim(),
                    CityAr = txtCityAr.Text.Trim(),
                    Region = txtRegion.Text.Trim(),
                    RegionAr = txtRegionAr.Text.Trim(),
                    PostalCode = txtPostalCode.Text.Trim(),
                    Status = cmbStatus.Text,
                    SubscriptionStatus = cmbSubscriptionStatus.Text,
                    Notes = txtNotes.Text.Trim()
                };

                // Generate pharmacy code if not in edit mode
                if (!isEditMode)
                {
                    pharmacy.PharmacyCode = PharmacyInfo.GeneratePharmacyCodeAdvanced(
                        pharmacy.CityAr,
                        pharmacy.PharmacyNameAr
                    );
                }

                // Parse coordinates if provided
                if (!string.IsNullOrWhiteSpace(txtLatitude.Text))
                {
                    if (decimal.TryParse(txtLatitude.Text, out decimal lat))
                        pharmacy.Latitude = lat;
                }

                if (!string.IsNullOrWhiteSpace(txtLongitude.Text))
                {
                    if (decimal.TryParse(txtLongitude.Text, out decimal lng))
                        pharmacy.Longitude = lng;
                }

                bool success;
                if (isEditMode)
                {
                    pharmacy.Id = currentPharmacy.Id;
                    success = PharmacyManager.UpdatePharmacy(pharmacy);
                }
                else
                {
                    success = PharmacyManager.AddPharmacy(pharmacy);
                }

                if (success)
                {
                    string message = isEditMode ?
                        $"تم تحديث بيانات الصيدلية '{pharmacy.PharmacyName}' بنجاح في قاعدة البيانات" :
                        $"تم إضافة الصيدلية '{pharmacy.PharmacyName}' بنجاح إلى قاعدة البيانات\n\nكود الصيدلية: {pharmacy.PharmacyCode}";

                    MessageBox.Show(message, "تم الحفظ بنجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    if (!isEditMode)
                    {
                        ClearForm();

                        // Show confirmation with database count
                        try
                        {
                            var allPharmacies = PharmacyManager.GetAllPharmacies();
                            MessageBox.Show($"إجمالي الصيدليات في قاعدة البيانات الآن: {allPharmacies.Rows.Count}",
                                          "تأكيد الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"تم حفظ الصيدلية لكن حدث خطأ في التحقق: {ex.Message}",
                                          "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("فشل في حفظ بيانات الصيدلية في قاعدة البيانات\nتحقق من الاتصال بقاعدة البيانات",
                                  "خطأ في الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                string errorMessage = $"خطأ في حفظ البيانات: {ex.Message}";

                // Add more specific error information
                if (ex.Message.Contains("رقم الترخيص موجود مسبقاً"))
                {
                    errorMessage = "رقم الترخيص موجود مسبقاً. يرجى استخدام رقم ترخيص مختلف.";
                }
                else if (ex.Message.Contains("البريد الإلكتروني موجود مسبقاً"))
                {
                    errorMessage = "البريد الإلكتروني موجود مسبقاً. يرجى استخدام بريد إلكتروني مختلف.";
                }
                else if (ex.Message.Contains("خطأ في الاتصال") || ex.Message.Contains("connection"))
                {
                    errorMessage = "خطأ في الاتصال بقاعدة البيانات. تحقق من:\n" +
                                 "1. تشغيل SQL Server\n" +
                                 "2. وجود قاعدة البيانات 'UnifiedPharmacy'\n" +
                                 "3. صلاحيات الوصول لقاعدة البيانات";
                }
                else if (ex.Message.Contains("Invalid object name 'pharmacies'"))
                {
                    errorMessage = "جدول الصيدليات غير موجود في قاعدة البيانات.\n" +
                                 "يرجى تنفيذ ملف Database_Setup.sql أولاً.";
                }

                MessageBox.Show(errorMessage, "خطأ في الحفظ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtPharmacyName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الصيدلية بالإنجليزية", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPharmacyName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPharmacyNameAr.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الصيدلية بالعربية", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPharmacyNameAr.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtOwnerName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المالك بالإنجليزية", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtOwnerName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtOwnerNameAr.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المالك بالعربية", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtOwnerNameAr.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtLicenseNumber.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الترخيص", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtLicenseNumber.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtEmail.Text) || !IsValidEmail(txtEmail.Text))
            {
                MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPhone.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الهاتف", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPhone.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtAddress.Text))
            {
                MessageBox.Show("يرجى إدخال العنوان بالإنجليزية", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAddress.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtAddressAr.Text))
            {
                MessageBox.Show("يرجى إدخال العنوان بالعربية", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtAddressAr.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtCity.Text))
            {
                MessageBox.Show("يرجى إدخال المدينة بالإنجليزية", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCity.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtCityAr.Text))
            {
                MessageBox.Show("يرجى إدخال المدينة بالعربية", "تحقق من البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCityAr.Focus();
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void ClearForm()
        {
            txtPharmacyName.Clear();
            txtPharmacyNameAr.Clear();
            txtOwnerName.Clear();
            txtOwnerNameAr.Clear();
            txtLicenseNumber.Clear();
            txtTaxNumber.Clear();
            txtEmail.Clear();
            txtPhone.Clear();
            txtMobile.Clear();
            txtAddress.Clear();
            txtAddressAr.Clear();
            txtCity.Clear();
            txtCityAr.Clear();
            txtRegion.Clear();
            txtRegionAr.Clear();
            txtPostalCode.Clear();
            txtLatitude.Clear();
            txtLongitude.Clear();
            cmbStatus.SelectedIndex = 0;
            cmbSubscriptionStatus.SelectedIndex = 0;
            txtNotes.Clear();
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد مسح جميع البيانات؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                ClearForm();
            }
        }
    }
}
