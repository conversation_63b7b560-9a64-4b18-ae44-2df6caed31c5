-- =============================================
-- إصلاح أعمدة جدول الصيدليات المفقودة
-- Fix Missing Pharmacy Table Columns
-- =============================================

USE pharmacy;
GO

-- التحقق من وجود الأعمدة المفقودة وإضافتها
PRINT 'بدء إصلاح أعمدة جدول الصيدليات...';

-- إضافة عمود subscriptionStatus إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionStatus')
BEGIN
    ALTER TABLE pharmacies ADD subscriptionStatus NVARCHAR(20) NOT NULL DEFAULT 'Trial';
    PRINT 'تم إضافة عمود subscriptionStatus';
END
ELSE
BEGIN
    PRINT 'عمود subscriptionStatus موجود مسبقاً';
END

-- إضافة عمود subscriptionStartDate إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionStartDate')
BEGIN
    ALTER TABLE pharmacies ADD subscriptionStartDate DATETIME2 DEFAULT GETDATE();
    PRINT 'تم إضافة عمود subscriptionStartDate';
END
ELSE
BEGIN
    PRINT 'عمود subscriptionStartDate موجود مسبقاً';
END

-- إضافة عمود subscriptionEndDate إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionEndDate')
BEGIN
    ALTER TABLE pharmacies ADD subscriptionEndDate DATETIME2;
    PRINT 'تم إضافة عمود subscriptionEndDate';
END
ELSE
BEGIN
    PRINT 'عمود subscriptionEndDate موجود مسبقاً';
END

-- إضافة عمود approvedBy إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'approvedBy')
BEGIN
    ALTER TABLE pharmacies ADD approvedBy INT;
    PRINT 'تم إضافة عمود approvedBy';
END
ELSE
BEGIN
    PRINT 'عمود approvedBy موجود مسبقاً';
END

-- إضافة عمود approvalDate إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'approvalDate')
BEGIN
    ALTER TABLE pharmacies ADD approvalDate DATETIME2;
    PRINT 'تم إضافة عمود approvalDate';
END
ELSE
BEGIN
    PRINT 'عمود approvalDate موجود مسبقاً';
END

-- إضافة عمود lastActivityDate إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'lastActivityDate')
BEGIN
    ALTER TABLE pharmacies ADD lastActivityDate DATETIME2;
    PRINT 'تم إضافة عمود lastActivityDate';
END
ELSE
BEGIN
    PRINT 'عمود lastActivityDate موجود مسبقاً';
END

-- إضافة عمود notes إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'notes')
BEGIN
    ALTER TABLE pharmacies ADD notes NVARCHAR(300);
    PRINT 'تم إضافة عمود notes';
END
ELSE
BEGIN
    PRINT 'عمود notes موجود مسبقاً';
END

-- إضافة عمود isActive إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'isActive')
BEGIN
    ALTER TABLE pharmacies ADD isActive BIT NOT NULL DEFAULT 1;
    PRINT 'تم إضافة عمود isActive';
END
ELSE
BEGIN
    PRINT 'عمود isActive موجود مسبقاً';
END

-- إضافة عمود registrationDate إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'registrationDate')
BEGIN
    ALTER TABLE pharmacies ADD registrationDate DATETIME2 NOT NULL DEFAULT GETDATE();
    PRINT 'تم إضافة عمود registrationDate';
END
ELSE
BEGIN
    PRINT 'عمود registrationDate موجود مسبقاً';
END

-- تحديث البيانات الموجودة
UPDATE pharmacies 
SET subscriptionStartDate = GETDATE(),
    subscriptionEndDate = DATEADD(MONTH, 1, GETDATE()),
    lastActivityDate = GETDATE(),
    registrationDate = GETDATE()
WHERE subscriptionStartDate IS NULL OR registrationDate IS NULL;

PRINT 'تم إصلاح جدول الصيدليات بنجاح!';

-- عرض بنية الجدول المحدثة
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'pharmacies'
ORDER BY ORDINAL_POSITION;

PRINT 'تم عرض بنية الجدول المحدثة';
