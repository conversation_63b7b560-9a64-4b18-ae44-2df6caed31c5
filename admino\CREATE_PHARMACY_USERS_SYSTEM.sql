-- =============================================
-- إنشاء نظام إدارة حسابات الصيدليات
-- Create Pharmacy Users Management System
-- =============================================

USE UnifiedPharmacy;
GO

PRINT 'بدء إنشاء نظام إدارة حسابات الصيدليات...';

-- =============================================
-- 1. جدول أدوار المستخدمين
-- User Roles Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_roles' AND xtype='U')
BEGIN
    CREATE TABLE user_roles (
        id INT IDENTITY(1,1) PRIMARY KEY,
        roleName NVARCHAR(30) UNIQUE NOT NULL,
        roleNameAr NVARCHAR(30) NOT NULL,
        description NVARCHAR(200),
        descriptionAr NVARCHAR(200),
        permissions NVARCHAR(MAX), -- JSON format for permissions
        isActive BIT NOT NULL DEFAULT 1,
        createdDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        createdBy INT
    );
    PRINT '✅ تم إنشاء جدول user_roles';
END
ELSE
BEGIN
    PRINT '⚠️ جدول user_roles موجود مسبقاً';
END

-- =============================================
-- 2. جدول مستخدمي الصيدليات
-- Pharmacy Users Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='pharmacy_users' AND xtype='U')
BEGIN
    CREATE TABLE pharmacy_users (
        id INT IDENTITY(1,1) PRIMARY KEY,
        pharmacyId INT NOT NULL,
        username NVARCHAR(50) UNIQUE NOT NULL,
        password NVARCHAR(255) NOT NULL,
        fullName NVARCHAR(100) NOT NULL,
        fullNameAr NVARCHAR(100) NOT NULL,
        email NVARCHAR(100) UNIQUE NOT NULL,
        phone NVARCHAR(20),
        mobile NVARCHAR(20),
        roleId INT NOT NULL,
        isActive BIT NOT NULL DEFAULT 1,
        lastLogin DATETIME2,
        loginAttempts INT NOT NULL DEFAULT 0,
        lockedUntil DATETIME2,
        createdDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        createdBy INT,
        modifiedDate DATETIME2,
        modifiedBy INT,
        profileImage NVARCHAR(255),
        notes NVARCHAR(500),
        sessionToken NVARCHAR(255),
        sessionExpiry DATETIME2,
        
        -- Foreign Keys
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id) ON DELETE CASCADE,
        FOREIGN KEY (roleId) REFERENCES user_roles(id),
        FOREIGN KEY (createdBy) REFERENCES pharmacy_users(id),
        FOREIGN KEY (modifiedBy) REFERENCES pharmacy_users(id)
    );
    PRINT '✅ تم إنشاء جدول pharmacy_users';
END
ELSE
BEGIN
    PRINT '⚠️ جدول pharmacy_users موجود مسبقاً';
END

-- =============================================
-- 3. جدول صلاحيات المستخدمين
-- User Permissions Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_permissions' AND xtype='U')
BEGIN
    CREATE TABLE user_permissions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        permissionName NVARCHAR(50) UNIQUE NOT NULL,
        permissionNameAr NVARCHAR(50) NOT NULL,
        description NVARCHAR(200),
        descriptionAr NVARCHAR(200),
        category NVARCHAR(30) NOT NULL,
        isActive BIT NOT NULL DEFAULT 1,
        createdDate DATETIME2 NOT NULL DEFAULT GETDATE()
    );
    PRINT '✅ تم إنشاء جدول user_permissions';
END
ELSE
BEGIN
    PRINT '⚠️ جدول user_permissions موجود مسبقاً';
END

-- =============================================
-- 4. جدول ربط المستخدمين بالصلاحيات
-- User-Permission Mapping Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_permission_mapping' AND xtype='U')
BEGIN
    CREATE TABLE user_permission_mapping (
        id INT IDENTITY(1,1) PRIMARY KEY,
        userId INT NOT NULL,
        permissionId INT NOT NULL,
        isActive BIT NOT NULL DEFAULT 1,
        grantedBy INT,
        grantedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        expiryDate DATETIME2,
        
        -- Foreign Keys
        FOREIGN KEY (userId) REFERENCES pharmacy_users(id) ON DELETE CASCADE,
        FOREIGN KEY (permissionId) REFERENCES user_permissions(id),
        FOREIGN KEY (grantedBy) REFERENCES pharmacy_users(id),
        
        -- Unique constraint
        UNIQUE(userId, permissionId)
    );
    PRINT '✅ تم إنشاء جدول user_permission_mapping';
END
ELSE
BEGIN
    PRINT '⚠️ جدول user_permission_mapping موجود مسبقاً';
END

-- =============================================
-- 5. جدول جلسات المستخدمين
-- User Sessions Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_sessions' AND xtype='U')
BEGIN
    CREATE TABLE user_sessions (
        id INT IDENTITY(1,1) PRIMARY KEY,
        userId INT NOT NULL,
        sessionToken NVARCHAR(255) UNIQUE NOT NULL,
        ipAddress NVARCHAR(45),
        userAgent NVARCHAR(500),
        loginTime DATETIME2 NOT NULL DEFAULT GETDATE(),
        lastActivity DATETIME2 NOT NULL DEFAULT GETDATE(),
        logoutTime DATETIME2,
        isActive BIT NOT NULL DEFAULT 1,
        
        -- Foreign Key
        FOREIGN KEY (userId) REFERENCES pharmacy_users(id) ON DELETE CASCADE
    );
    PRINT '✅ تم إنشاء جدول user_sessions';
END
ELSE
BEGIN
    PRINT '⚠️ جدول user_sessions موجود مسبقاً';
END

-- =============================================
-- 6. جدول سجل الأنشطة
-- Activity Log Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_activity_log' AND xtype='U')
BEGIN
    CREATE TABLE user_activity_log (
        id INT IDENTITY(1,1) PRIMARY KEY,
        userId INT,
        pharmacyId INT,
        action NVARCHAR(50) NOT NULL,
        actionAr NVARCHAR(50) NOT NULL,
        tableName NVARCHAR(50),
        recordId INT,
        oldValues NVARCHAR(MAX),
        newValues NVARCHAR(MAX),
        description NVARCHAR(500),
        descriptionAr NVARCHAR(500),
        ipAddress NVARCHAR(45),
        userAgent NVARCHAR(500),
        timestamp DATETIME2 NOT NULL DEFAULT GETDATE(),
        
        -- Foreign Keys
        FOREIGN KEY (userId) REFERENCES pharmacy_users(id),
        FOREIGN KEY (pharmacyId) REFERENCES pharmacies(id)
    );
    PRINT '✅ تم إنشاء جدول user_activity_log';
END
ELSE
BEGIN
    PRINT '⚠️ جدول user_activity_log موجود مسبقاً';
END

PRINT '';
PRINT '🎉 تم إنشاء جميع جداول نظام إدارة حسابات الصيدليات بنجاح!';
