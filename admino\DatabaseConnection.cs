using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

namespace admino
{
    public class DatabaseConnection
    {
        // Connection strings
        private static readonly string AdminConnectionString = 
            "Data Source=.;Initial Catalog=PharmacyAdminSystem;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False";
        
        private static readonly string PharmacyConnectionString =
            "Data Source=.;Initial Catalog=UnifiedPharmacy;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False";

        // Get connection to Admin System database
        public static SqlConnection GetAdminConnection()
        {
            try
            {
                SqlConnection connection = new SqlConnection(AdminConnectionString);
                return connection;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الاتصال بقاعدة البيانات المركزية: {ex.Message}");
            }
        }

        // Get connection to Pharmacy database
        public static SqlConnection GetPharmacyConnection()
        {
            try
            {
                SqlConnection connection = new SqlConnection(PharmacyConnectionString);
                return connection;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الاتصال بقاعدة بيانات الصيدلية: {ex.Message}");
            }
        }

        // Test Admin database connection
        public static bool TestAdminConnection()
        {
            try
            {
                using (SqlConnection connection = GetAdminConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        // Test Pharmacy database connection
        public static bool TestPharmacyConnection()
        {
            try
            {
                using (SqlConnection connection = GetPharmacyConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        // Execute query on Admin database
        public static DataTable ExecuteAdminQuery(string query, SqlParameter[] parameters = null)
        {
            DataTable dataTable = new DataTable();
            
            try
            {
                using (SqlConnection connection = GetAdminConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(dataTable);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام على قاعدة البيانات المركزية: {ex.Message}");
            }
            
            return dataTable;
        }

        // Execute query on Pharmacy database
        public static DataTable ExecutePharmacyQuery(string query, SqlParameter[] parameters = null)
        {
            DataTable dataTable = new DataTable();
            
            try
            {
                using (SqlConnection connection = GetPharmacyConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(dataTable);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام على قاعدة بيانات الصيدلية: {ex.Message}");
            }
            
            return dataTable;
        }

        // Execute non-query on Admin database (INSERT, UPDATE, DELETE)
        public static int ExecuteAdminNonQuery(string query, SqlParameter[] parameters = null)
        {
            try
            {
                using (SqlConnection connection = GetAdminConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        return command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ العملية على قاعدة البيانات المركزية: {ex.Message}");
            }
        }

        // Execute non-query on Pharmacy database (INSERT, UPDATE, DELETE)
        public static int ExecutePharmacyNonQuery(string query, SqlParameter[] parameters = null)
        {
            try
            {
                using (SqlConnection connection = GetPharmacyConnection())
                {
                    connection.Open();
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        return command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ العملية على قاعدة بيانات الصيدلية: {ex.Message}");
            }
        }
    }
}
