# 🚨 دليل الإصلاح السريع - مشكلة إضافة وتعديل الصيدليات

## المشكلة الحالية
- ✅ **قائمة الصيدليات تعمل** (تعرض البيانات)
- ❌ **إضافة صيدلية لا تعمل** (خطأ في عمود Notes)
- ❌ **تعديل صيدلية لا يعمل** (نفس المشكلة)

## السبب
عمود `notes` مفقود أو له اسم مختلف في جدول `pharmacies` في قاعدة البيانات `UnifiedPharmacy`

## الحل السريع (3 خطوات)

### الخطوة 1: تشخيص المشكلة
```sql
-- افتح SQL Server Management Studio ونفذ:
USE UnifiedPharmacy;
SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'pharmacies' 
ORDER BY ORDINAL_POSITION;
```

### الخطوة 2: الإصلاح الشامل
**نفذ أحد الملفات التالية:**

#### الخيار الأول - الإصلاح الشامل (مستحسن):
```
افتح ملف: COMPLETE_FIX_UnifiedPharmacy.sql
نفذه كاملاً في SQL Server Management Studio
```

#### الخيار الثاني - الإصلاح اليدوي:
```sql
USE UnifiedPharmacy;
GO

-- حذف الجدول القديم وإعادة إنشاؤه
DROP TABLE IF EXISTS pharmacies;

CREATE TABLE pharmacies (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pharmacyName NVARCHAR(100) NOT NULL,
    pharmacyNameAr NVARCHAR(100) NOT NULL,
    ownerName NVARCHAR(100) NOT NULL,
    ownerNameAr NVARCHAR(100) NOT NULL,
    licenseNumber NVARCHAR(50) UNIQUE NOT NULL,
    taxNumber NVARCHAR(50),
    email NVARCHAR(100) UNIQUE NOT NULL,
    phone NVARCHAR(20) NOT NULL,
    mobile NVARCHAR(20),
    address NVARCHAR(200) NOT NULL,
    addressAr NVARCHAR(200) NOT NULL,
    city NVARCHAR(50) NOT NULL,
    cityAr NVARCHAR(50) NOT NULL,
    region NVARCHAR(50),
    regionAr NVARCHAR(50),
    postalCode NVARCHAR(10),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    status NVARCHAR(20) NOT NULL DEFAULT 'Pending',
    isActive BIT NOT NULL DEFAULT 1,
    registrationDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    approvalDate DATETIME2,
    approvedBy INT,
    lastActivityDate DATETIME2,
    subscriptionStatus NVARCHAR(20) NOT NULL DEFAULT 'Trial',
    subscriptionStartDate DATETIME2 DEFAULT GETDATE(),
    subscriptionEndDate DATETIME2,
    notes NVARCHAR(300)
);

-- إدراج بيانات تجريبية
INSERT INTO pharmacies (
    pharmacyName, pharmacyNameAr, ownerName, ownerNameAr, licenseNumber,
    email, phone, address, addressAr, city, cityAr
) VALUES 
('Al-Noor Pharmacy', 'صيدلية النور', 'Ahmed Ali', 'أحمد علي', 'LIC001',
 '<EMAIL>', '**********', 'King Fahd Road', 'شارع الملك فهد', 'Riyadh', 'الرياض'),
('Al-Shifa Pharmacy', 'صيدلية الشفاء', 'Fatima Ahmed', 'فاطمة أحمد', 'LIC002',
 '<EMAIL>', '**********', 'King Abdulaziz Road', 'طريق الملك عبدالعزيز', 'Riyadh', 'الرياض');
```

### الخطوة 3: اختبار النظام
1. **شغل البرنامج**
2. **سجل دخول:** `admin` / `Admin@123`
3. **اختبر إضافة صيدلية:**
   - اذهب إلى "إضافة صيدلية"
   - املأ البيانات الأساسية
   - اضغط "حفظ"
4. **اختبر تعديل صيدلية:**
   - اذهب إلى "قائمة الصيدليات"
   - اضغط "تعديل" على أي صيدلية
   - عدل البيانات واضغط "حفظ"

## إذا استمرت المشكلة

### تحقق من:
1. **اسم قاعدة البيانات:** يجب أن يكون `UnifiedPharmacy`
2. **اتصال SQL Server:** تأكد من تشغيله
3. **صلاحيات المستخدم:** تأكد من صلاحيات الكتابة

### رسائل الخطأ الشائعة:
- **"Invalid column name 'notes'"** → نفذ الإصلاح الشامل
- **"Cannot open database"** → تحقق من اسم قاعدة البيانات
- **"Invalid object name 'pharmacies'"** → أنشئ الجدول

## ملفات الإصلاح المتاحة:
- ✅ **`COMPLETE_FIX_UnifiedPharmacy.sql`** - الإصلاح الشامل (مستحسن)
- ✅ **`Fix_UnifiedPharmacy_Columns.sql`** - إضافة الأعمدة فقط
- ✅ **`DIAGNOSE_DATABASE.sql`** - تشخيص المشكلة

## النتيجة المتوقعة:
بعد تطبيق الإصلاح:
- ✅ إضافة صيدلية جديدة تعمل
- ✅ تعديل الصيدليات يعمل
- ✅ قائمة الصيدليات تعمل
- ✅ نظام الفلترة يعمل

---
**💡 نصيحة:** استخدم `COMPLETE_FIX_UnifiedPharmacy.sql` للحصول على أفضل النتائج
